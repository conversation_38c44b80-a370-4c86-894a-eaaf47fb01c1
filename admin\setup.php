<?php
/**
 * Admin Setup Script
 * 
 * Creates default admin user and sets up admin tables
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

require_once '../api/config.php';

// Default admin credentials
$defaultAdmin = [
    'name' => 'Admin User',
    'email' => '<EMAIL>',
    'password' => 'admin123', // Change this in production!
    'role' => 'admin'
];

try {
    $pdo = getDBConnection();
    
    echo "<h1>ForexClass Admin Setup</h1>";
    
    // Check if admin table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
    if (!$stmt->fetch()) {
        // Create admins table
        $pdo->exec("
            CREATE TABLE admins (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('admin', 'super_admin') DEFAULT 'admin',
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        echo "<p>✅ Created admins table</p>";
    } else {
        echo "<p>ℹ️ Admins table already exists</p>";
    }
    
    // Check if admin sessions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_sessions'");
    if (!$stmt->fetch()) {
        // Create admin sessions table
        $pdo->exec("
            CREATE TABLE admin_sessions (
                id INT PRIMARY KEY AUTO_INCREMENT,
                admin_id INT NOT NULL,
                session_token VARCHAR(255) NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
            )
        ");
        echo "<p>✅ Created admin_sessions table</p>";
    } else {
        echo "<p>ℹ️ Admin sessions table already exists</p>";
    }
    
    // Check if default admin exists
    $stmt = $pdo->prepare("SELECT id FROM admins WHERE email = ?");
    $stmt->execute([$defaultAdmin['email']]);
    
    if (!$stmt->fetch()) {
        // Create default admin
        $hashedPassword = password_hash($defaultAdmin['password'], PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("
            INSERT INTO admins (name, email, password, role, is_active) 
            VALUES (?, ?, ?, ?, 1)
        ");
        $stmt->execute([
            $defaultAdmin['name'],
            $defaultAdmin['email'],
            $hashedPassword,
            $defaultAdmin['role']
        ]);
        
        echo "<p>✅ Created default admin user</p>";
        echo "<div style='background: #f0f9ff; border: 1px solid #0ea5e9; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h3>Default Admin Credentials:</h3>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($defaultAdmin['email']) . "</p>";
        echo "<p><strong>Password:</strong> " . htmlspecialchars($defaultAdmin['password']) . "</p>";
        echo "<p style='color: #dc2626;'><strong>⚠️ Important:</strong> Change the password after first login!</p>";
        echo "</div>";
    } else {
        echo "<p>ℹ️ Default admin user already exists</p>";
    }
    
    // Check subscription plans table
    $stmt = $pdo->query("SHOW TABLES LIKE 'subscription_plans'");
    if (!$stmt->fetch()) {
        // Create subscription plans table
        $pdo->exec("
            CREATE TABLE subscription_plans (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                plan_type VARCHAR(50) UNIQUE NOT NULL,
                description TEXT,
                price_monthly DECIMAL(10,2) NOT NULL,
                price_yearly DECIMAL(10,2) NOT NULL,
                features JSON,
                telegram_access BOOLEAN DEFAULT FALSE,
                signal_access BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        echo "<p>✅ Created subscription_plans table</p>";
        
        // Insert default plans
        $defaultPlans = [
            [
                'name' => 'Basic Plan',
                'plan_type' => 'basic',
                'description' => 'Perfect for beginners starting their forex journey',
                'price_monthly' => 2500,
                'price_yearly' => 25000,
                'features' => json_encode([
                    'Access to basic forex courses',
                    'Basic trading signals',
                    'Email support',
                    'Community forum access'
                ]),
                'telegram_access' => 0,
                'signal_access' => 1
            ],
            [
                'name' => 'Premium Plan',
                'plan_type' => 'premium',
                'description' => 'Advanced features for serious traders',
                'price_monthly' => 5000,
                'price_yearly' => 50000,
                'features' => json_encode([
                    'All basic plan features',
                    'Advanced trading courses',
                    'Premium trading signals',
                    'Telegram community access',
                    'Priority email support',
                    'Weekly market analysis'
                ]),
                'telegram_access' => 1,
                'signal_access' => 1
            ],
            [
                'name' => 'VIP Plan',
                'plan_type' => 'vip',
                'description' => 'Ultimate package for professional traders',
                'price_monthly' => 10000,
                'price_yearly' => 100000,
                'features' => json_encode([
                    'All premium plan features',
                    'VIP trading signals',
                    '1-on-1 mentoring sessions',
                    'Direct WhatsApp support',
                    'Custom trading strategies',
                    'Exclusive market insights'
                ]),
                'telegram_access' => 1,
                'signal_access' => 1
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO subscription_plans 
            (name, plan_type, description, price_monthly, price_yearly, features, telegram_access, signal_access) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($defaultPlans as $plan) {
            $stmt->execute([
                $plan['name'],
                $plan['plan_type'],
                $plan['description'],
                $plan['price_monthly'],
                $plan['price_yearly'],
                $plan['features'],
                $plan['telegram_access'],
                $plan['signal_access']
            ]);
        }
        
        echo "<p>✅ Created default subscription plans</p>";
    } else {
        echo "<p>ℹ️ Subscription plans table already exists</p>";
    }
    
    echo "<h2>Setup Complete!</h2>";
    echo "<p>You can now access the admin dashboard:</p>";
    echo "<ul>";
    echo "<li><a href='index.php'>Admin Login</a></li>";
    echo "<li><a href='dashboard.php'>Admin Dashboard</a> (requires login)</li>";
    echo "<li><a href='../test-plans.php'>Test Plans Sync</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h1>Setup Error</h1>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2 {
    color: #333;
}
p {
    margin: 10px 0;
}
a {
    color: #4f46e5;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
ul {
    margin: 15px 0;
}
li {
    margin: 5px 0;
}
</style>
