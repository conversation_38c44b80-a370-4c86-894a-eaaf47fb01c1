<?php
/**
 * M-Pesa Callback Handler - Enhanced Version
 * 
 * Handles M-Pesa payment callbacks and status checks
 * 
 * <AUTHOR> Team
 * @version 2.0
 */

// Set CORS headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// Log callback data for debugging
function logCallback($data, $type = 'callback') {
    $logFile = __DIR__ . '/logs/mpesa_' . $type . '_' . date('Y-m-d') . '.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = date('Y-m-d H:i:s') . ' - ' . json_encode($data) . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Send JSON response
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit();
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'POST':
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        if ($action === 'check_status') {
            checkPaymentStatus($input);
        } else {
            // Handle M-Pesa callback
            handleMpesaCallback($input);
        }
        break;
        
    case 'GET':
        // Handle status check via GET
        $checkoutRequestId = $_GET['checkout_request_id'] ?? '';
        if ($checkoutRequestId) {
            checkPaymentStatusByRequestId($checkoutRequestId);
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid request'], 400);
        }
        break;
        
    default:
        sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
        break;
}

function handleMpesaCallback($callbackData) {
    // Log the callback for debugging
    logCallback($callbackData, 'callback');
    
    try {
        $pdo = getDBConnection();
        
        // Extract callback data
        $stkCallback = $callbackData['Body']['stkCallback'] ?? null;
        
        if (!$stkCallback) {
            logCallback(['error' => 'Invalid callback structure'], 'error');
            sendJsonResponse(['ResultCode' => 0, 'ResultDesc' => 'Success']);
            return;
        }
        
        $merchantRequestID = $stkCallback['MerchantRequestID'] ?? '';
        $checkoutRequestID = $stkCallback['CheckoutRequestID'] ?? '';
        $resultCode = $stkCallback['ResultCode'] ?? 1;
        $resultDesc = $stkCallback['ResultDesc'] ?? '';
        
        // Update transaction status
        $status = ($resultCode == 0) ? 'completed' : 'failed';
        
        $stmt = $pdo->prepare("
            UPDATE transactions 
            SET status = ?, mpesa_receipt_number = ?, result_desc = ?, updated_at = NOW()
            WHERE checkout_request_id = ?
        ");
        
        $receiptNumber = '';
        if ($resultCode == 0 && isset($stkCallback['CallbackMetadata']['Item'])) {
            // Extract receipt number from callback metadata
            foreach ($stkCallback['CallbackMetadata']['Item'] as $item) {
                if ($item['Name'] === 'MpesaReceiptNumber') {
                    $receiptNumber = $item['Value'];
                    break;
                }
            }
        }
        
        $stmt->execute([$status, $receiptNumber, $resultDesc, $checkoutRequestID]);
        
        // If payment successful, activate subscription
        if ($resultCode == 0) {
            activateSubscription($checkoutRequestID, $pdo);
        }
        
        logCallback([
            'checkout_request_id' => $checkoutRequestID,
            'status' => $status,
            'receipt_number' => $receiptNumber
        ], 'processed');
        
        // Respond to M-Pesa
        sendJsonResponse(['ResultCode' => 0, 'ResultDesc' => 'Success']);
        
    } catch (Exception $e) {
        logCallback(['error' => $e->getMessage()], 'error');
        sendJsonResponse(['ResultCode' => 1, 'ResultDesc' => 'Failed']);
    }
}

function checkPaymentStatus($input) {
    $checkoutRequestId = $input['checkout_request_id'] ?? '';
    
    if (!$checkoutRequestId) {
        sendJsonResponse(['success' => false, 'message' => 'Checkout request ID required'], 400);
    }
    
    try {
        $pdo = getDBConnection();
        
        $stmt = $pdo->prepare("
            SELECT status, mpesa_receipt_number, result_desc, updated_at
            FROM transactions 
            WHERE checkout_request_id = ?
        ");
        $stmt->execute([$checkoutRequestId]);
        $transaction = $stmt->fetch();
        
        if (!$transaction) {
            sendJsonResponse([
                'success' => false,
                'status' => 'not_found',
                'message' => 'Transaction not found'
            ]);
        }
        
        sendJsonResponse([
            'success' => true,
            'status' => $transaction['status'],
            'receipt_number' => $transaction['mpesa_receipt_number'],
            'message' => $transaction['result_desc'],
            'updated_at' => $transaction['updated_at']
        ]);
        
    } catch (Exception $e) {
        logCallback(['status_check_error' => $e->getMessage()], 'error');
        sendJsonResponse([
            'success' => false,
            'status' => 'error',
            'message' => 'Failed to check payment status'
        ], 500);
    }
}

function checkPaymentStatusByRequestId($checkoutRequestId) {
    checkPaymentStatus(['checkout_request_id' => $checkoutRequestId]);
}

function activateSubscription($checkoutRequestID, $pdo) {
    try {
        // Get transaction details
        $stmt = $pdo->prepare("
            SELECT t.*, us.id as subscription_id 
            FROM transactions t
            LEFT JOIN user_subscriptions us ON t.subscription_id = us.id
            WHERE t.checkout_request_id = ?
        ");
        $stmt->execute([$checkoutRequestID]);
        $transaction = $stmt->fetch();
        
        if (!$transaction) {
            logCallback(['error' => 'Transaction not found for checkout request: ' . $checkoutRequestID], 'error');
            return;
        }
        
        // Activate the subscription
        if ($transaction['subscription_id']) {
            $stmt = $pdo->prepare("
                UPDATE user_subscriptions 
                SET status = 'active', activated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$transaction['subscription_id']]);
            
            logCallback([
                'subscription_activated' => $transaction['subscription_id'],
                'user_id' => $transaction['user_id']
            ], 'activation');
        }
        
    } catch (Exception $e) {
        logCallback(['activation_error' => $e->getMessage()], 'error');
    }
}
?>
