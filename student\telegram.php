<?php
/**
 * Student Telegram Page
 * 
 * Telegram community integration and management
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

session_start();
require_once '../api/config.php';

// Check if student is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'user') {
    header('Location: index.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Student';

$page_title = "Telegram Community - ForexClass Student Portal";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="user-id" content="<?php echo $user_id; ?>">
    <meta name="user-name" content="<?php echo htmlspecialchars($user_name); ?>">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <h2>ForexClass</h2>
                    <span>Student Portal</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="courses.php" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            <span>My Courses</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="subscription.php" class="nav-link">
                            <i class="fas fa-crown"></i>
                            <span>Subscription</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="profile.php" class="nav-link">
                            <i class="fas fa-user"></i>
                            <span>Profile</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="signals.php" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span>Trading Signals</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="telegram.php" class="nav-link">
                            <i class="fab fa-telegram"></i>
                            <span>Telegram</span>
                        </a>
                    </li>
                </ul>
                
                <div class="sidebar-footer">
                    <a href="../" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>Back to Website</span>
                    </a>
                    <a href="#logout" class="nav-link logout" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="dashboard-header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="page-title">
                        <h1><i class="fab fa-telegram"></i> Telegram Community</h1>
                        <p>Connect with fellow traders and get exclusive content</p>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="telegram-status">
                        <div class="status-indicator active"></div>
                        <span>Online</span>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <span class="user-name"><?php echo htmlspecialchars($user_name); ?></span>
                            <span class="user-role">Student</span>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Telegram Content -->
            <div class="dashboard-content">
                <!-- Community Overview -->
                <div class="community-overview">
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="card-content">
                            <h3>2,847</h3>
                            <p>Community Members</p>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="card-content">
                            <h3>156</h3>
                            <p>Messages Today</p>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-content">
                            <h3>12</h3>
                            <p>Signals Shared</p>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="card-content">
                            <h3>4.9</h3>
                            <p>Community Rating</p>
                        </div>
                    </div>
                </div>
                
                <!-- Telegram Channels -->
                <div class="telegram-channels">
                    <h3>Available Channels</h3>
                    
                    <div class="channels-grid">
                        <!-- Main Channel -->
                        <div class="channel-card featured">
                            <div class="channel-header">
                                <div class="channel-icon">
                                    <i class="fab fa-telegram"></i>
                                </div>
                                <div class="channel-info">
                                    <h4>ForexClass Main</h4>
                                    <p>@forexclass_main</p>
                                </div>
                                <div class="channel-badge">Free</div>
                            </div>
                            
                            <div class="channel-description">
                                <p>Join our main community channel for daily market updates, educational content, and general discussions.</p>
                            </div>
                            
                            <div class="channel-stats">
                                <div class="stat">
                                    <i class="fas fa-users"></i>
                                    <span>2,847 members</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-clock"></i>
                                    <span>Active 24/7</span>
                                </div>
                            </div>
                            
                            <div class="channel-features">
                                <ul>
                                    <li><i class="fas fa-check"></i> Daily market analysis</li>
                                    <li><i class="fas fa-check"></i> Educational content</li>
                                    <li><i class="fas fa-check"></i> Community discussions</li>
                                    <li><i class="fas fa-check"></i> News updates</li>
                                </ul>
                            </div>
                            
                            <button class="btn btn-primary btn-telegram" onclick="joinChannel('main')">
                                <i class="fab fa-telegram"></i>
                                Join Channel
                            </button>
                        </div>
                        
                        <!-- Premium Signals Channel -->
                        <div class="channel-card premium">
                            <div class="channel-header">
                                <div class="channel-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <div class="channel-info">
                                    <h4>Premium Signals</h4>
                                    <p>@forexclass_signals</p>
                                </div>
                                <div class="channel-badge premium">Premium</div>
                            </div>
                            
                            <div class="channel-description">
                                <p>Exclusive trading signals channel for premium subscribers with high-accuracy signals and detailed analysis.</p>
                            </div>
                            
                            <div class="channel-stats">
                                <div class="stat">
                                    <i class="fas fa-users"></i>
                                    <span>456 members</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-chart-line"></i>
                                    <span>78% win rate</span>
                                </div>
                            </div>
                            
                            <div class="channel-features">
                                <ul>
                                    <li><i class="fas fa-check"></i> Live trading signals</li>
                                    <li><i class="fas fa-check"></i> Entry & exit points</li>
                                    <li><i class="fas fa-check"></i> Risk management</li>
                                    <li><i class="fas fa-check"></i> Performance tracking</li>
                                </ul>
                            </div>
                            
                            <button class="btn btn-premium" onclick="joinPremiumChannel('signals')" id="signalsChannelBtn">
                                <i class="fas fa-lock"></i>
                                Requires Premium
                            </button>
                        </div>
                        
                        <!-- VIP Channel -->
                        <div class="channel-card vip">
                            <div class="channel-header">
                                <div class="channel-icon">
                                    <i class="fas fa-gem"></i>
                                </div>
                                <div class="channel-info">
                                    <h4>VIP Traders</h4>
                                    <p>@forexclass_vip</p>
                                </div>
                                <div class="channel-badge vip">VIP</div>
                            </div>
                            
                            <div class="channel-description">
                                <p>Exclusive VIP channel for our top-tier members with direct access to professional traders and mentors.</p>
                            </div>
                            
                            <div class="channel-stats">
                                <div class="stat">
                                    <i class="fas fa-users"></i>
                                    <span>89 members</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-medal"></i>
                                    <span>Elite traders</span>
                                </div>
                            </div>
                            
                            <div class="channel-features">
                                <ul>
                                    <li><i class="fas fa-check"></i> 1-on-1 mentoring</li>
                                    <li><i class="fas fa-check"></i> Advanced strategies</li>
                                    <li><i class="fas fa-check"></i> Portfolio reviews</li>
                                    <li><i class="fas fa-check"></i> Direct trader access</li>
                                </ul>
                            </div>
                            
                            <button class="btn btn-vip" onclick="joinVipChannel('vip')" id="vipChannelBtn">
                                <i class="fas fa-lock"></i>
                                Requires VIP
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="telegram-activity">
                    <div class="section-header">
                        <h3><i class="fas fa-activity"></i> Recent Activity</h3>
                        <button class="btn btn-outline" onclick="refreshActivity()">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                    </div>
                    
                    <div class="activity-feed">
                        <div class="activity-item">
                            <div class="activity-avatar">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="activity-content">
                                <h4>New Signal Posted</h4>
                                <p>EUR/USD BUY signal shared in Premium Signals channel</p>
                                <span class="activity-time">5 minutes ago</span>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-avatar">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="activity-content">
                                <h4>New Members Joined</h4>
                                <p>15 new traders joined the main community today</p>
                                <span class="activity-time">2 hours ago</span>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-avatar">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="activity-content">
                                <h4>Educational Content</h4>
                                <p>New trading tutorial shared in the main channel</p>
                                <span class="activity-time">4 hours ago</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Telegram Bot Integration -->
                <div class="telegram-bot">
                    <div class="section-header">
                        <h3><i class="fas fa-robot"></i> ForexClass Bot</h3>
                    </div>
                    
                    <div class="bot-info">
                        <div class="bot-card">
                            <div class="bot-header">
                                <div class="bot-avatar">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="bot-details">
                                    <h4>@ForexClassBot</h4>
                                    <p>Your personal trading assistant</p>
                                </div>
                            </div>
                            
                            <div class="bot-features">
                                <h5>Bot Features:</h5>
                                <ul>
                                    <li><i class="fas fa-bell"></i> Signal notifications</li>
                                    <li><i class="fas fa-chart-bar"></i> Market updates</li>
                                    <li><i class="fas fa-calculator"></i> Pip calculator</li>
                                    <li><i class="fas fa-question-circle"></i> Trading help</li>
                                </ul>
                            </div>
                            
                            <button class="btn btn-primary" onclick="startBot()">
                                <i class="fas fa-play"></i>
                                Start Bot
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="dashboard.js"></script>
    <script src="telegram.js"></script>
</body>
</html>
