<?php
// Add Package - Clean URL version
// This file handles the /addpackage route

session_start();
require_once '../api/config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin/login');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $plan_type = trim($_POST['plan_type'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price_monthly = floatval($_POST['price_monthly'] ?? 0);
    $price_yearly = floatval($_POST['price_yearly'] ?? 0);
    $features = $_POST['features'] ?? [];
    $course_access_level = intval($_POST['course_access_level'] ?? 1);
    $max_devices = intval($_POST['max_devices'] ?? 2);
    $support_level = $_POST['support_level'] ?? 'basic';
    $telegram_access = isset($_POST['telegram_access']);
    $signal_access = isset($_POST['signal_access']);
    $is_active = isset($_POST['is_active']);
    
    // Validation
    $errors = [];
    
    if (empty($name)) {
        $errors[] = "Package name is required";
    }
    
    if (empty($plan_type)) {
        $errors[] = "Plan type is required";
    }
    
    if (empty($description)) {
        $errors[] = "Description is required";
    }
    
    if ($price_monthly <= 0) {
        $errors[] = "Monthly price must be greater than 0";
    }
    
    if ($price_yearly <= 0) {
        $errors[] = "Yearly price must be greater than 0";
    }
    
    if (empty($features)) {
        $errors[] = "At least one feature is required";
    }
    
    if (empty($errors)) {
        try {
            $pdo = getDBConnection();
            
            // Check if plan_type already exists
            $stmt = $pdo->prepare("SELECT id FROM subscription_plans WHERE plan_type = ?");
            $stmt->execute([$plan_type]);
            
            if ($stmt->fetch()) {
                $errors[] = "Plan type already exists";
            } else {
                // Create package
                $stmt = $pdo->prepare("
                    INSERT INTO subscription_plans (
                        name, plan_type, description, price_monthly, price_yearly, currency,
                        features, telegram_access, course_access_level, signal_access,
                        support_level, max_devices, is_active, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                ");
                
                $featuresJson = json_encode(array_filter($features));
                
                if ($stmt->execute([
                    $name, $plan_type, $description, $price_monthly, $price_yearly, 'KES',
                    $featuresJson, $telegram_access, $course_access_level, $signal_access,
                    $support_level, $max_devices, $is_active
                ])) {
                    $success = "Package created successfully!";
                    // Clear form
                    $name = $plan_type = $description = '';
                    $price_monthly = $price_yearly = 0;
                    $features = [];
                } else {
                    $errors[] = "Failed to create package";
                }
            }
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

$page_title = "Add Package - Admin";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-box"></i> Add New Package</h1>
            <a href="/admin/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
        
        <div class="form-container">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="package-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Package Name</label>
                        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="plan_type">Plan Type</label>
                        <input type="text" id="plan_type" name="plan_type" value="<?php echo htmlspecialchars($plan_type ?? ''); ?>" required>
                        <small>Unique identifier (lowercase, no spaces)</small>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" rows="3" required><?php echo htmlspecialchars($description ?? ''); ?></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="price_monthly">Monthly Price (KSH)</label>
                        <input type="number" id="price_monthly" name="price_monthly" value="<?php echo $price_monthly ?? ''; ?>" step="0.01" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="price_yearly">Yearly Price (KSH)</label>
                        <input type="number" id="price_yearly" name="price_yearly" value="<?php echo $price_yearly ?? ''; ?>" step="0.01" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Features</label>
                    <div id="features-container">
                        <?php 
                        $savedFeatures = $features ?? [''];
                        foreach ($savedFeatures as $index => $feature): 
                        ?>
                            <div class="feature-input">
                                <input type="text" name="features[]" value="<?php echo htmlspecialchars($feature); ?>" placeholder="Enter feature">
                                <button type="button" onclick="removeFeature(this)" class="btn-remove">×</button>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <button type="button" onclick="addFeature()" class="btn btn-small">Add Feature</button>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="course_access_level">Course Access Level</label>
                        <select id="course_access_level" name="course_access_level">
                            <option value="1" <?php echo ($course_access_level ?? 1) == 1 ? 'selected' : ''; ?>>Level 1 - Basic</option>
                            <option value="2" <?php echo ($course_access_level ?? 1) == 2 ? 'selected' : ''; ?>>Level 2 - Intermediate</option>
                            <option value="3" <?php echo ($course_access_level ?? 1) == 3 ? 'selected' : ''; ?>>Level 3 - Advanced</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="max_devices">Max Devices</label>
                        <input type="number" id="max_devices" name="max_devices" value="<?php echo $max_devices ?? 2; ?>" min="1" max="10">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="support_level">Support Level</label>
                        <select id="support_level" name="support_level">
                            <option value="basic" <?php echo ($support_level ?? 'basic') === 'basic' ? 'selected' : ''; ?>>Basic Support</option>
                            <option value="priority" <?php echo ($support_level ?? 'basic') === 'priority' ? 'selected' : ''; ?>>Priority Support</option>
                            <option value="premium" <?php echo ($support_level ?? 'basic') === 'premium' ? 'selected' : ''; ?>>Premium Support</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Options</label>
                        <div class="checkbox-group">
                            <label>
                                <input type="checkbox" name="telegram_access" <?php echo ($telegram_access ?? false) ? 'checked' : ''; ?>>
                                Telegram Access
                            </label>
                            <label>
                                <input type="checkbox" name="signal_access" <?php echo ($signal_access ?? true) ? 'checked' : ''; ?>>
                                Trading Signals
                            </label>
                            <label>
                                <input type="checkbox" name="is_active" <?php echo ($is_active ?? true) ? 'checked' : ''; ?>>
                                Active Package
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Package
                    </button>
                    <a href="/admin/dashboard" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
    
    <style>
        .admin-container { max-width: 1000px; margin: 40px auto; padding: 20px; }
        .admin-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; }
        .form-container { background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .package-form { max-width: 800px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 600; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .form-group small { color: #666; font-size: 12px; }
        .feature-input { display: flex; margin-bottom: 10px; }
        .feature-input input { flex: 1; margin-right: 10px; }
        .btn-remove { background: #dc3545; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; }
        .checkbox-group label { display: block; margin-bottom: 10px; font-weight: normal; }
        .checkbox-group input[type="checkbox"] { width: auto; margin-right: 10px; }
        .form-actions { margin-top: 30px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; text-decoration: none; display: inline-block; margin-right: 10px; cursor: pointer; }
        .btn-small { padding: 5px 10px; font-size: 12px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .alert { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert ul { margin: 0; padding-left: 20px; }
    </style>
    
    <script>
        function addFeature() {
            const container = document.getElementById('features-container');
            const div = document.createElement('div');
            div.className = 'feature-input';
            div.innerHTML = `
                <input type="text" name="features[]" placeholder="Enter feature">
                <button type="button" onclick="removeFeature(this)" class="btn-remove">×</button>
            `;
            container.appendChild(div);
        }
        
        function removeFeature(button) {
            button.parentElement.remove();
        }
    </script>
</body>
</html>
