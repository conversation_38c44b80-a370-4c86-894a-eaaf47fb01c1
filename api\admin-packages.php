<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Function to verify admin session
function verifyAdminSession() {
    $headers = getallheaders();
    $token = null;

    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    }

    if (!$token) {
        sendJsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
    }

    try {
        $pdo = getDBConnection();

        // Verify admin token using user_sessions table (same as regular users but check role)
        $stmt = $pdo->prepare("
            SELECT u.*, us.expires_at
            FROM users u
            JOIN user_sessions us ON u.id = us.user_id
            WHERE us.token = ? AND us.expires_at > ? AND u.status = 'active' AND u.role = 'admin'
        ");
        $stmt->execute([$token, date('Y-m-d H:i:s')]);
        $admin = $stmt->fetch();

        if (!$admin) {
            sendJsonResponse(['success' => false, 'message' => 'Invalid or expired admin session'], 401);
        }

        return $admin;
    } catch (Exception $e) {
        logError("Admin session verification error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Authentication failed'], 500);
    }
}

// Get all packages
function getPackages() {
    $admin = verifyAdminSession();
    
    try {
        $pdo = getDBConnection();
        
        $stmt = $pdo->prepare("
            SELECT 
                sp.*,
                COUNT(us.id) as active_subscribers
            FROM subscription_plans sp
            LEFT JOIN user_subscriptions us ON sp.plan_type = us.plan_type AND us.status = 'active'
            GROUP BY sp.id
            ORDER BY sp.price_monthly ASC
        ");
        $stmt->execute();
        $packages = $stmt->fetchAll();
        
        // Parse features JSON
        foreach ($packages as &$package) {
            $package['features'] = $package['features'] ? json_decode($package['features'], true) : [];
        }
        
        sendJsonResponse([
            'success' => true,
            'packages' => $packages
        ]);
        
    } catch (Exception $e) {
        logError("Get packages error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to load packages'], 500);
    }
}

// Create new package
function createPackage() {
    $admin = verifyAdminSession();
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid input data'], 400);
    }
    
    // Validate required fields
    $required = ['name', 'plan_type', 'description', 'price_monthly', 'price_yearly', 'features'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            sendJsonResponse(['success' => false, 'message' => "Field '$field' is required"], 400);
        }
    }
    
    try {
        $pdo = getDBConnection();
        
        // Check if plan_type already exists
        $stmt = $pdo->prepare("SELECT id FROM subscription_plans WHERE plan_type = ?");
        $stmt->execute([$input['plan_type']]);
        if ($stmt->fetch()) {
            sendJsonResponse(['success' => false, 'message' => 'Plan type already exists'], 400);
        }
        
        // Create package
        $stmt = $pdo->prepare("
            INSERT INTO subscription_plans (
                name, plan_type, description, price_monthly, price_yearly, currency,
                features, telegram_access, course_access_level, signal_access,
                support_level, max_devices, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $input['name'],
            $input['plan_type'],
            $input['description'],
            $input['price_monthly'],
            $input['price_yearly'],
            $input['currency'] ?? 'KES',
            json_encode($input['features']),
            $input['telegram_access'] ?? false,
            $input['course_access_level'] ?? 1,
            $input['signal_access'] ?? true,
            $input['support_level'] ?? 'basic',
            $input['max_devices'] ?? 2,
            $input['is_active'] ?? true,
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s')
        ]);
        
        $packageId = $pdo->lastInsertId();
        
        // Update website pricing if needed
        updateWebsitePricing();
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Package created successfully',
            'package_id' => $packageId
        ]);
        
    } catch (Exception $e) {
        logError("Create package error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to create package'], 500);
    }
}

// Update package
function updatePackage() {
    $admin = verifyAdminSession();
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || empty($input['id'])) {
        sendJsonResponse(['success' => false, 'message' => 'Package ID is required'], 400);
    }
    
    try {
        $pdo = getDBConnection();
        
        // Check if package exists
        $stmt = $pdo->prepare("SELECT * FROM subscription_plans WHERE id = ?");
        $stmt->execute([$input['id']]);
        $package = $stmt->fetch();
        
        if (!$package) {
            sendJsonResponse(['success' => false, 'message' => 'Package not found'], 404);
        }
        
        // Build update query dynamically
        $updateFields = [];
        $params = [];
        
        $allowedFields = [
            'name', 'description', 'price_monthly', 'price_yearly', 'currency',
            'telegram_access', 'course_access_level', 'signal_access',
            'support_level', 'max_devices', 'is_active'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "$field = ?";
                $params[] = $input[$field];
            }
        }
        
        // Handle features separately
        if (isset($input['features'])) {
            $updateFields[] = "features = ?";
            $params[] = json_encode($input['features']);
        }
        
        if (empty($updateFields)) {
            sendJsonResponse(['success' => false, 'message' => 'No fields to update'], 400);
        }
        
        $updateFields[] = "updated_at = ?";
        $params[] = date('Y-m-d H:i:s');
        $params[] = $input['id'];
        
        $sql = "UPDATE subscription_plans SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        // Update website pricing if needed
        updateWebsitePricing();
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Package updated successfully'
        ]);
        
    } catch (Exception $e) {
        logError("Update package error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to update package'], 500);
    }
}

// Delete package
function deletePackage() {
    $admin = verifyAdminSession();
    $packageId = $_GET['id'] ?? 0;
    
    if (!$packageId) {
        sendJsonResponse(['success' => false, 'message' => 'Package ID is required'], 400);
    }
    
    try {
        $pdo = getDBConnection();
        
        // Check if package has active subscriptions
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count FROM user_subscriptions us
            JOIN subscription_plans sp ON us.plan_type = sp.plan_type
            WHERE sp.id = ? AND us.status = 'active'
        ");
        $stmt->execute([$packageId]);
        $activeSubscriptions = $stmt->fetch()['count'];
        
        if ($activeSubscriptions > 0) {
            sendJsonResponse([
                'success' => false, 
                'message' => "Cannot delete package with $activeSubscriptions active subscriptions"
            ], 400);
        }
        
        // Delete package
        $stmt = $pdo->prepare("DELETE FROM subscription_plans WHERE id = ?");
        $stmt->execute([$packageId]);
        
        if ($stmt->rowCount() > 0) {
            // Update website pricing if needed
            updateWebsitePricing();
            
            sendJsonResponse([
                'success' => true,
                'message' => 'Package deleted successfully'
            ]);
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Package not found'], 404);
        }
        
    } catch (Exception $e) {
        logError("Delete package error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to delete package'], 500);
    }
}

// Update website pricing (this would update a JSON file or cache that the website reads)
function updateWebsitePricing() {
    try {
        $pdo = getDBConnection();
        
        // Get active packages
        $stmt = $pdo->prepare("
            SELECT name, plan_type, description, price_monthly, price_yearly, features
            FROM subscription_plans 
            WHERE is_active = TRUE 
            ORDER BY price_monthly ASC
        ");
        $stmt->execute();
        $packages = $stmt->fetchAll();
        
        // Parse features
        foreach ($packages as &$package) {
            $package['features'] = $package['features'] ? json_decode($package['features'], true) : [];
        }
        
        // Save to JSON file that website can read
        $pricingData = [
            'last_updated' => date('Y-m-d H:i:s'),
            'packages' => $packages
        ];
        
        file_put_contents('../pricing-data.json', json_encode($pricingData, JSON_PRETTY_PRINT));
        
    } catch (Exception $e) {
        logError("Update website pricing error: " . $e->getMessage());
    }
}

// Handle different request methods and actions
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

switch ($method) {
    case 'GET':
        if ($action === 'list') {
            getPackages();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;
        
    case 'POST':
        if ($action === 'create') {
            createPackage();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;
        
    case 'PUT':
        if ($action === 'update') {
            updatePackage();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;
        
    case 'DELETE':
        if ($action === 'delete') {
            deletePackage();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;
        
    default:
        sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
        break;
}
?>
