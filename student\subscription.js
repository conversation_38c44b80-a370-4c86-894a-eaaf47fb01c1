// Student Subscription JavaScript with M-Pesa Integration
document.addEventListener('DOMContentLoaded', function() {
    initializeSubscription();
});

let currentPlan = {};
let paymentTimer = null;

function initializeSubscription() {
    loadSubscriptionData();
    initializeLogout();
}

function loadSubscriptionData() {
    loadPaymentHistory();
}

async function loadPaymentHistory() {
    try {
        const userIdMeta = document.querySelector('meta[name="user-id"]');
        const userId = userIdMeta ? userIdMeta.content : null;
        
        if (!userId) return;
        
        const response = await fetch('../api/profile.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_payment_history',
                user_id: userId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Update total spent
            document.getElementById('totalSpent').textContent = 'KSH ' + new Intl.NumberFormat().format(data.summary.total_spent);
        }
        
    } catch (error) {
        console.error('Error loading payment history:', error);
    }
}

function selectPlan(planType, billingCycle, amount) {
    currentPlan = {
        planType: planType,
        billingCycle: billingCycle,
        amount: amount
    };
    
    // Show M-Pesa payment modal
    showPaymentModal();
}

function showPaymentModal() {
    // Create modal HTML
    const modalHTML = `
        <div class="modal-overlay" id="paymentModal">
            <div class="modal">
                <div class="modal-header">
                    <h3 id="modalTitle">Subscribe to ${currentPlan.planType.charAt(0).toUpperCase() + currentPlan.planType.slice(1)} Plan</h3>
                    <button class="modal-close" onclick="closePaymentModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Payment Step -->
                    <div class="modal-step active" id="paymentStep">
                        <div class="payment-summary">
                            <h4>${currentPlan.planType.charAt(0).toUpperCase() + currentPlan.planType.slice(1)} Plan</h4>
                            <p>${currentPlan.billingCycle.charAt(0).toUpperCase() + currentPlan.billingCycle.slice(1)} subscription</p>
                            <div class="amount-display">
                                <span class="currency">KSH</span>
                                <span class="amount">${new Intl.NumberFormat().format(currentPlan.amount)}</span>
                                <span class="period">/${currentPlan.billingCycle}</span>
                            </div>
                        </div>
                        
                        <form id="paymentForm">
                            <div class="form-group">
                                <label for="phoneNumber">M-Pesa Phone Number</label>
                                <div class="phone-input">
                                    <span class="country-code">+254</span>
                                    <input type="tel" id="phoneNumber" placeholder="7XXXXXXXX" maxlength="9" required>
                                </div>
                                <small>Enter your M-Pesa registered phone number</small>
                            </div>
                            
                            <div class="payment-actions">
                                <button type="button" class="btn btn-secondary" onclick="closePaymentModal()">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-primary" id="payBtn">
                                    <i class="fas fa-mobile-alt"></i>
                                    Pay with M-Pesa
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Processing Step -->
                    <div class="modal-step" id="processingStep" style="display: none;">
                        <div class="processing-animation">
                            <div class="phone-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="spinner"></div>
                        </div>
                        <h4>Processing Payment</h4>
                        <p>Check your phone for the M-Pesa prompt and enter your PIN to complete the payment.</p>
                        <div class="processing-timer">
                            <span>Time remaining: </span>
                            <span id="countdown">120</span>
                            <span> seconds</span>
                        </div>
                    </div>
                    
                    <!-- Success Step -->
                    <div class="modal-step" id="successStep" style="display: none;">
                        <div class="success-animation">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h4>🎉 Payment Successful!</h4>
                        <p>Your subscription has been activated successfully. Welcome to your new plan!</p>
                        <div class="success-actions">
                            <button class="btn btn-primary" onclick="window.location.reload()">
                                <i class="fas fa-refresh"></i>
                                Refresh Page
                            </button>
                            <button class="btn btn-secondary" onclick="closePaymentModal()">
                                Close
                            </button>
                        </div>
                    </div>
                    
                    <!-- Error Step -->
                    <div class="modal-step" id="errorStep" style="display: none;">
                        <div class="error-animation">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <h4>Payment Failed</h4>
                        <p id="errorMessage">Payment was not completed. Please try again.</p>
                        <div class="error-actions">
                            <button class="btn btn-secondary" onclick="closePaymentModal()">Close</button>
                            <button class="btn btn-primary" onclick="retryPayment()">
                                <i class="fas fa-redo"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Initialize form handler
    const paymentForm = document.getElementById('paymentForm');
    if (paymentForm) {
        paymentForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            await processPayment();
        });
    }
}

function closePaymentModal() {
    const modal = document.getElementById('paymentModal');
    if (modal) {
        modal.remove();
    }
    
    if (paymentTimer) {
        clearInterval(paymentTimer);
        paymentTimer = null;
    }
}

function showModalStep(stepId) {
    // Hide all steps
    document.querySelectorAll('.modal-step').forEach(step => {
        step.style.display = 'none';
    });
    
    // Show selected step
    const selectedStep = document.getElementById(stepId);
    if (selectedStep) {
        selectedStep.style.display = 'block';
    }
}

async function processPayment() {
    const phoneNumber = document.getElementById('phoneNumber').value;
    const payBtn = document.getElementById('payBtn');
    
    // Validate phone number
    if (!phoneNumber || phoneNumber.length !== 9) {
        alert('Please enter a valid phone number (9 digits)');
        return;
    }
    
    // Format phone number
    const formattedPhone = '254' + phoneNumber;
    
    // Get user ID
    const userIdMeta = document.querySelector('meta[name="user-id"]');
    const userId = userIdMeta ? userIdMeta.content : null;
    
    if (!userId) {
        alert('User session error. Please refresh and try again.');
        return;
    }
    
    // Disable button and show loading
    payBtn.disabled = true;
    payBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    
    try {
        const response = await fetch('../api/mpesa.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone: formattedPhone,
                amount: currentPlan.amount,
                plan_type: currentPlan.planType,
                billing_cycle: currentPlan.billingCycle,
                user_id: userId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Show processing step
            showModalStep('processingStep');
            startPaymentTimer();
            
            // Check payment status
            checkPaymentStatus(data.checkout_request_id);
        } else {
            showError(data.message || 'Payment initiation failed');
        }
        
    } catch (error) {
        console.error('Payment error:', error);
        showError('Network error. Please try again.');
    } finally {
        // Reset button
        payBtn.disabled = false;
        payBtn.innerHTML = '<i class="fas fa-mobile-alt"></i> Pay with M-Pesa';
    }
}

function startPaymentTimer() {
    let timeLeft = 120; // 2 minutes
    const countdownEl = document.getElementById('countdown');
    
    paymentTimer = setInterval(() => {
        timeLeft--;
        if (countdownEl) {
            countdownEl.textContent = timeLeft;
        }
        
        if (timeLeft <= 0) {
            clearInterval(paymentTimer);
            showError('Payment timeout. Please try again.');
        }
    }, 1000);
}

async function checkPaymentStatus(checkoutRequestId) {
    let attempts = 0;
    const maxAttempts = 24; // Check for 2 minutes (5 second intervals)
    
    const statusCheck = setInterval(async () => {
        attempts++;
        
        try {
            const response = await fetch('../api/callback.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'check_status',
                    checkout_request_id: checkoutRequestId
                })
            });
            
            const data = await response.json();
            
            if (data.status === 'completed') {
                clearInterval(statusCheck);
                if (paymentTimer) clearInterval(paymentTimer);
                showSuccess();
            } else if (data.status === 'failed' || attempts >= maxAttempts) {
                clearInterval(statusCheck);
                if (paymentTimer) clearInterval(paymentTimer);
                showError(data.message || 'Payment verification failed');
            }
            
        } catch (error) {
            console.error('Status check error:', error);
        }
    }, 5000); // Check every 5 seconds
}

function showSuccess() {
    showModalStep('successStep');
}

function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    showModalStep('errorStep');
}

function retryPayment() {
    showModalStep('paymentStep');
}

function showPlans() {
    // Scroll to plans section
    document.getElementById('availablePlans').scrollIntoView({ 
        behavior: 'smooth' 
    });
}

function manageBilling() {
    alert('Billing management functionality will be available soon. Please contact support for assistance.');
}

function initializeLogout() {
    const logoutBtn = document.getElementById('logoutBtn');
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (confirm('Are you sure you want to logout?')) {
                // Clear user data
                localStorage.removeItem('userInfo');
                localStorage.removeItem('userToken');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('authToken');
                
                // Call logout API to destroy session
                fetch('../api/logout.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(() => {
                    // Redirect to home page
                    window.location.href = '../';
                }).catch(() => {
                    // Redirect anyway
                    window.location.href = '../';
                });
            }
        });
    }
}
