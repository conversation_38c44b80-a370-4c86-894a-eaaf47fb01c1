<?php
/**
 * Test Admin Authentication
 * 
 * Simple test to verify admin login is working
 */

require_once '../api/config.php';

echo "<h1>Admin Authentication Test</h1>";

try {
    $pdo = getDBConnection();
    
    // Check if admins table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
    if ($stmt->fetch()) {
        echo "<p>✅ Admins table exists</p>";
        
        // Check if default admin exists
        $stmt = $pdo->prepare("SELECT id, name, email, role, is_active FROM admins WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<p>✅ Default admin user exists:</p>";
            echo "<ul>";
            echo "<li><strong>ID:</strong> " . $admin['id'] . "</li>";
            echo "<li><strong>Name:</strong> " . htmlspecialchars($admin['name']) . "</li>";
            echo "<li><strong>Email:</strong> " . htmlspecialchars($admin['email']) . "</li>";
            echo "<li><strong>Role:</strong> " . htmlspecialchars($admin['role']) . "</li>";
            echo "<li><strong>Active:</strong> " . ($admin['is_active'] ? 'Yes' : 'No') . "</li>";
            echo "</ul>";
        } else {
            echo "<p>❌ Default admin user not found</p>";
            echo "<p><a href='setup.php'>Run Setup Script</a></p>";
        }
        
        // Check admin_sessions table
        $stmt = $pdo->query("SHOW TABLES LIKE 'admin_sessions'");
        if ($stmt->fetch()) {
            echo "<p>✅ Admin sessions table exists</p>";
        } else {
            echo "<p>❌ Admin sessions table missing</p>";
        }
        
    } else {
        echo "<p>❌ Admins table does not exist</p>";
        echo "<p><a href='setup.php'>Run Setup Script</a></p>";
    }
    
    echo "<h2>Test Login</h2>";
    echo "<p>Try logging in with these credentials:</p>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> <EMAIL></li>";
    echo "<li><strong>Password:</strong> admin123</li>";
    echo "</ul>";
    
    echo "<h2>Manual Login Test</h2>";
    echo "<form method='post' action='admin-auth.php?action=login' style='max-width: 400px; margin: 20px 0;'>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label>Email:</label><br>";
    echo "<input type='email' name='email' value='<EMAIL>' style='width: 100%; padding: 8px; margin-top: 5px;'>";
    echo "</div>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label>Password:</label><br>";
    echo "<input type='password' name='password' value='admin123' style='width: 100%; padding: 8px; margin-top: 5px;'>";
    echo "</div>";
    echo "<button type='submit' style='background: #4f46e5; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test Login</button>";
    echo "</form>";
    
    echo "<h2>Links</h2>";
    echo "<ul>";
    echo "<li><a href='index.php'>Admin Login Page</a></li>";
    echo "<li><a href='dashboard.php'>Admin Dashboard</a></li>";
    echo "<li><a href='setup.php'>Setup Script</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2 {
    color: #333;
}
a {
    color: #4f46e5;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
