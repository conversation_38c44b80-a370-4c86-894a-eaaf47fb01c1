// Student Trading Signals JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeSignals();
});

function initializeSignals() {
    loadSignalData();
    initializeFilters();
    initializeLogout();
    startAutoRefresh();
}

function loadSignalData() {
    loadLiveSignals();
    loadRecentSignals();
    updateSignalStats();
}

async function loadLiveSignals() {
    const grid = document.getElementById('liveSignalsGrid');
    
    try {
        // Mock data - replace with actual API call
        const liveSignals = [
            {
                id: 1,
                pair: 'EUR/USD',
                type: 'BUY',
                status: 'active',
                entry: 1.0850,
                stopLoss: 1.0820,
                takeProfit: 1.0920,
                currentPrice: 1.0875,
                pips: 25,
                timeAgo: '2 hours ago',
                progress: 35
            },
            {
                id: 2,
                pair: 'GBP/USD',
                type: 'SELL',
                status: 'active',
                entry: 1.2650,
                stopLoss: 1.2680,
                takeProfit: 1.2580,
                currentPrice: 1.2630,
                pips: 20,
                timeAgo: '1 hour ago',
                progress: 28
            },
            {
                id: 3,
                pair: 'USD/JPY',
                type: 'BUY',
                status: 'pending',
                entry: 150.50,
                stopLoss: 150.00,
                takeProfit: 151.50,
                currentPrice: 150.30,
                pips: 0,
                timeAgo: '30 minutes ago',
                progress: 0
            }
        ];
        
        let signalsHTML = '';
        
        liveSignals.forEach(signal => {
            const profitClass = signal.pips >= 0 ? 'positive' : 'negative';
            const typeClass = signal.type.toLowerCase();
            
            signalsHTML += `
                <div class="signal-card live">
                    <div class="signal-header">
                        <div class="signal-pair">
                            <span class="pair">${signal.pair}</span>
                            <span class="signal-type ${typeClass}">${signal.type}</span>
                        </div>
                        <div class="signal-status">
                            <span class="status ${signal.status}">${signal.status.toUpperCase()}</span>
                            <span class="time">${signal.timeAgo}</span>
                        </div>
                    </div>
                    
                    <div class="signal-details">
                        <div class="price-levels">
                            <div class="level">
                                <label>Entry:</label>
                                <span class="price">${signal.entry}</span>
                            </div>
                            <div class="level">
                                <label>Stop Loss:</label>
                                <span class="price">${signal.stopLoss}</span>
                            </div>
                            <div class="level">
                                <label>Take Profit:</label>
                                <span class="price">${signal.takeProfit}</span>
                            </div>
                        </div>
                        
                        <div class="signal-progress">
                            <div class="progress-info">
                                <span>Current: ${signal.currentPrice}</span>
                                <span class="profit ${profitClass}">${signal.pips >= 0 ? '+' : ''}${signal.pips} pips</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill ${profitClass}" style="width: ${signal.progress}%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="signal-actions">
                        <button class="btn btn-sm btn-outline" onclick="viewChart('${signal.pair}')">
                            <i class="fas fa-chart-line"></i>
                            View Chart
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="copySignal(${signal.id})">
                            <i class="fas fa-copy"></i>
                            Copy Signal
                        </button>
                    </div>
                </div>
            `;
        });
        
        grid.innerHTML = signalsHTML;
        
        // Update active signals count
        document.getElementById('activeSignalsCount').textContent = liveSignals.filter(s => s.status === 'active').length;
        
    } catch (error) {
        console.error('Error loading live signals:', error);
        grid.innerHTML = '<div class="error-message">Failed to load signals. Please try again.</div>';
    }
}

async function loadRecentSignals() {
    const table = document.getElementById('recentSignalsTable');
    
    try {
        // Mock data - replace with actual API call
        const recentSignals = [
            {
                pair: 'GBP/USD',
                type: 'SELL',
                entry: 1.2650,
                exit: 1.2580,
                pips: 70,
                status: 'win',
                date: 'Mar 20, 2024'
            },
            {
                pair: 'EUR/USD',
                type: 'BUY',
                entry: 1.0800,
                exit: 1.0780,
                pips: -20,
                status: 'loss',
                date: 'Mar 19, 2024'
            },
            {
                pair: 'USD/JPY',
                type: 'BUY',
                entry: 149.50,
                exit: 150.00,
                pips: 50,
                status: 'win',
                date: 'Mar 18, 2024'
            },
            {
                pair: 'AUD/USD',
                type: 'SELL',
                entry: 0.6650,
                exit: 0.6620,
                pips: 30,
                status: 'win',
                date: 'Mar 17, 2024'
            },
            {
                pair: 'EUR/GBP',
                type: 'BUY',
                entry: 0.8550,
                exit: 0.8530,
                pips: -20,
                status: 'loss',
                date: 'Mar 16, 2024'
            }
        ];
        
        let tableHTML = `
            <div class="table-header">
                <div class="col">Pair</div>
                <div class="col">Type</div>
                <div class="col">Entry</div>
                <div class="col">Exit</div>
                <div class="col">Pips</div>
                <div class="col">Status</div>
                <div class="col">Date</div>
            </div>
        `;
        
        recentSignals.forEach(signal => {
            const pipsClass = signal.pips >= 0 ? 'positive' : 'negative';
            const typeClass = signal.type.toLowerCase();
            
            tableHTML += `
                <div class="table-row">
                    <div class="col">
                        <span class="pair-name">${signal.pair}</span>
                    </div>
                    <div class="col">
                        <span class="signal-type ${typeClass}">${signal.type}</span>
                    </div>
                    <div class="col">${signal.entry}</div>
                    <div class="col">${signal.exit}</div>
                    <div class="col">
                        <span class="pips ${pipsClass}">${signal.pips >= 0 ? '+' : ''}${signal.pips}</span>
                    </div>
                    <div class="col">
                        <span class="status closed ${signal.status}">${signal.status.toUpperCase()}</span>
                    </div>
                    <div class="col">${signal.date}</div>
                </div>
            `;
        });
        
        table.innerHTML = tableHTML;
        
    } catch (error) {
        console.error('Error loading recent signals:', error);
        table.innerHTML = '<div class="error-message">Failed to load recent signals. Please try again.</div>';
    }
}

function updateSignalStats() {
    // Mock data - replace with actual calculations
    document.getElementById('winRate').textContent = '78%';
    document.getElementById('totalSignals').textContent = '156';
    document.getElementById('avgPips').textContent = '+45';
    document.getElementById('lastUpdate').textContent = '2 min';
}

function initializeFilters() {
    const filters = ['pairFilter', 'typeFilter', 'statusFilter'];
    
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', applyFilters);
        }
    });
}

function applyFilters() {
    const pairFilter = document.getElementById('pairFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    console.log('Applying filters:', { pairFilter, typeFilter, statusFilter });
    
    // Apply filters to signals
    // This would filter the displayed signals based on the selected criteria
    // For now, just reload the data
    loadSignalData();
}

function refreshSignals() {
    // Show loading state
    const refreshBtn = document.querySelector('.signal-filters .btn');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    refreshBtn.disabled = true;
    
    // Reload signal data
    setTimeout(() => {
        loadSignalData();
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;
        
        // Update last update time
        document.getElementById('lastUpdate').textContent = 'Just now';
    }, 1000);
}

function viewChart(pair) {
    // Open chart in new window or modal
    alert(`Opening chart for ${pair}. This would open a trading chart.`);
    // window.open(`../chart.php?pair=${pair}`, '_blank');
}

function copySignal(signalId) {
    // Copy signal details to clipboard
    alert(`Signal ${signalId} copied to clipboard. This would copy the signal details.`);
    
    // Example of copying to clipboard
    const signalText = `Signal copied: Check your trading platform for details.`;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(signalText).then(() => {
            showNotification('Signal copied to clipboard!', 'success');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = signalText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Signal copied to clipboard!', 'success');
    }
}

function viewAllSignals() {
    // Navigate to full signals history page
    alert('This would show all signals history.');
    // window.location.href = 'signals-history.php';
}

function startAutoRefresh() {
    // Auto-refresh signals every 30 seconds
    setInterval(() => {
        loadLiveSignals();
        updateSignalStats();
    }, 30000);
}

function showNotification(message, type = 'info') {
    // Create and show notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#4f46e5'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function initializeLogout() {
    const logoutBtn = document.getElementById('logoutBtn');
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (confirm('Are you sure you want to logout?')) {
                // Clear user data
                localStorage.removeItem('userInfo');
                localStorage.removeItem('userToken');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('authToken');
                
                // Call logout API to destroy session
                fetch('../api/logout.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(() => {
                    // Redirect to home page
                    window.location.href = '../';
                }).catch(() => {
                    // Redirect anyway
                    window.location.href = '../';
                });
            }
        });
    }
}

// Add CSS for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
