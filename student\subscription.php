<?php
/**
 * Student Subscription Management - New Version
 * 
 * Enhanced subscription management with M-Pesa integration
 * 
 * <AUTHOR> Team
 * @version 2.0
 */

session_start();
require_once '../api/config.php';

// Check if student is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'user') {
    header('Location: index.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Student';

// Get user subscription data
try {
    $pdo = getDBConnection();
    
    // Get current subscription
    $stmt = $pdo->prepare("
        SELECT us.*, sp.name as plan_name, sp.description, sp.features, 
               sp.price_monthly, sp.price_yearly, sp.telegram_access, sp.signal_access
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_type = sp.plan_type
        WHERE us.user_id = ? AND us.status = 'active' AND us.end_date > NOW()
        ORDER BY us.created_at DESC
        LIMIT 1
    ");
    $stmt->execute([$user_id]);
    $currentSubscription = $stmt->fetch();
    
    // Get available plans
    $stmt = $pdo->prepare("
        SELECT * FROM subscription_plans 
        WHERE is_active = 1 
        ORDER BY price_monthly ASC
    ");
    $stmt->execute();
    $availablePlans = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error = "Failed to load subscription data: " . $e->getMessage();
}

$page_title = "Manage Subscription - ForexClass Student Portal";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="user-id" content="<?php echo $user_id; ?>">
    <meta name="user-name" content="<?php echo htmlspecialchars($user_name); ?>">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <h2>ForexClass</h2>
                    <span>Student Portal</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="courses.php" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            <span>My Courses</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="subscription.php" class="nav-link">
                            <i class="fas fa-crown"></i>
                            <span>Subscription</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="profile.php" class="nav-link">
                            <i class="fas fa-user"></i>
                            <span>Profile</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="signals.php" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span>Trading Signals</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="telegram.php" class="nav-link">
                            <i class="fab fa-telegram"></i>
                            <span>Telegram</span>
                        </a>
                    </li>
                </ul>
                
                <div class="sidebar-footer">
                    <a href="../" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>Back to Website</span>
                    </a>
                    <a href="#logout" class="nav-link logout" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="dashboard-header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="page-title">
                        <h1><i class="fas fa-crown"></i> Manage Your Subscription</h1>
                        <p>Choose the perfect plan for your trading journey</p>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <span class="user-name"><?php echo htmlspecialchars($user_name); ?></span>
                            <span class="user-role">Student</span>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Subscription Content -->
            <div class="dashboard-content">
                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Subscription Overview -->
                <div class="subscription-overview">
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="currentPlanDisplay">
                                <?php echo $currentSubscription ? htmlspecialchars($currentSubscription['plan_name']) : 'Free Plan'; ?>
                            </h3>
                            <p>Your current subscription</p>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="daysRemaining">
                                <?php 
                                if ($currentSubscription) {
                                    $endDate = new DateTime($currentSubscription['end_date']);
                                    $now = new DateTime();
                                    $diff = $now->diff($endDate);
                                    echo $diff->days . ' days';
                                } else {
                                    echo '-';
                                }
                                ?>
                            </h3>
                            <p>Days remaining</p>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="totalSpent">KSH 0</h3>
                            <p>Total spent</p>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="nextBilling">
                                <?php 
                                if ($currentSubscription) {
                                    echo date('M j, Y', strtotime($currentSubscription['end_date']));
                                } else {
                                    echo '-';
                                }
                                ?>
                            </h3>
                            <p>Next billing</p>
                        </div>
                    </div>
                </div>
                
                <!-- Current Subscription Status -->
                <?php if ($currentSubscription): ?>
                <div class="current-subscription-status">
                    <div class="status-card active">
                        <div class="status-header">
                            <div class="status-info">
                                <h3><?php echo htmlspecialchars($currentSubscription['plan_name']); ?></h3>
                                <p><?php echo htmlspecialchars($currentSubscription['description'] ?? 'Premium subscription plan'); ?></p>
                            </div>
                            <div class="status-badge active">
                                <i class="fas fa-check-circle"></i>
                                Active
                            </div>
                        </div>
                        
                        <div class="status-details">
                            <div class="detail-item">
                                <span class="label">Billing Cycle:</span>
                                <span class="value"><?php echo ucfirst($currentSubscription['billing_cycle'] ?? 'monthly'); ?></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Amount:</span>
                                <span class="value">KSH <?php echo number_format($currentSubscription['amount']); ?></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Started:</span>
                                <span class="value"><?php echo date('M j, Y', strtotime($currentSubscription['start_date'])); ?></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Expires:</span>
                                <span class="value"><?php echo date('M j, Y', strtotime($currentSubscription['end_date'])); ?></span>
                            </div>
                        </div>
                        
                        <?php if ($currentSubscription['telegram_access']): ?>
                        <div class="status-actions">
                            <a href="https://t.me/forexclass" class="btn btn-telegram" target="_blank">
                                <i class="fab fa-telegram"></i>
                                Join Telegram
                            </a>
                            <button class="btn btn-outline" onclick="manageBilling()">
                                <i class="fas fa-cog"></i>
                                Manage Billing
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php else: ?>
                <div class="no-subscription">
                    <div class="no-sub-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3>No Active Subscription</h3>
                    <p>Unlock premium features and advanced trading tools with a subscription plan.</p>
                    <button class="btn btn-primary btn-large" onclick="showPlans()">
                        <i class="fas fa-rocket"></i>
                        Choose Your Plan
                    </button>
                </div>
                <?php endif; ?>
                
                <!-- Available Plans -->
                <div class="available-plans" id="availablePlans">
                    <div class="section-header">
                        <h3><i class="fas fa-star"></i> Available Plans</h3>
                        <p>Choose the plan that fits your trading goals</p>
                    </div>
                    
                    <div class="plans-grid">
                        <?php foreach ($availablePlans as $plan): ?>
                        <div class="plan-card <?php echo $currentSubscription && $currentSubscription['plan_type'] === $plan['plan_type'] ? 'current' : ''; ?>" 
                             data-plan="<?php echo $plan['plan_type']; ?>">
                            
                            <?php if ($plan['plan_type'] === 'premium'): ?>
                            <div class="plan-badge">Most Popular</div>
                            <?php endif; ?>
                            
                            <div class="plan-header">
                                <h4><?php echo htmlspecialchars($plan['name']); ?></h4>
                                <div class="plan-price">
                                    <span class="price">KSH <?php echo number_format($plan['price_monthly']); ?></span>
                                    <span class="period">/month</span>
                                </div>
                                <div class="yearly-price">
                                    KSH <?php echo number_format($plan['price_yearly']); ?>/year 
                                    <span class="savings">(Save <?php echo round((1 - ($plan['price_yearly'] / ($plan['price_monthly'] * 12))) * 100); ?>%)</span>
                                </div>
                            </div>
                            
                            <div class="plan-description">
                                <p><?php echo htmlspecialchars($plan['description']); ?></p>
                            </div>
                            
                            <?php 
                            $features = json_decode($plan['features'], true);
                            if ($features): 
                            ?>
                            <div class="plan-features">
                                <ul>
                                    <?php foreach ($features as $feature): ?>
                                    <li><i class="fas fa-check"></i> <?php echo htmlspecialchars($feature); ?></li>
                                    <?php endforeach; ?>
                                    
                                    <?php if ($plan['telegram_access']): ?>
                                    <li><i class="fas fa-check"></i> Telegram community access</li>
                                    <?php endif; ?>
                                    
                                    <?php if ($plan['signal_access']): ?>
                                    <li><i class="fas fa-check"></i> Premium trading signals</li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <?php endif; ?>
                            
                            <div class="plan-actions">
                                <?php if (!$currentSubscription || $currentSubscription['plan_type'] !== $plan['plan_type']): ?>
                                <button class="btn btn-primary" onclick="selectPlan('<?php echo $plan['plan_type']; ?>', 'monthly', <?php echo $plan['price_monthly']; ?>)">
                                    <i class="fas fa-mobile-alt"></i>
                                    Pay Monthly - KSH <?php echo number_format($plan['price_monthly']); ?>
                                </button>
                                <button class="btn btn-outline" onclick="selectPlan('<?php echo $plan['plan_type']; ?>', 'yearly', <?php echo $plan['price_yearly']; ?>)">
                                    <i class="fas fa-mobile-alt"></i>
                                    Pay Yearly - KSH <?php echo number_format($plan['price_yearly']); ?>
                                </button>
                                <?php else: ?>
                                <button class="btn btn-current" disabled>
                                    <i class="fas fa-check"></i>
                                    Current Plan
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="dashboard.js"></script>
    <script src="subscription.js"></script>
</body>
</html>
