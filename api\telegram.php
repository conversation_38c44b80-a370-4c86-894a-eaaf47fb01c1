<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Function to verify user session
function verifyUserSession() {
    $headers = getallheaders();
    $token = null;
    
    // Check for Authorization header
    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    }
    
    if (!$token) {
        sendJsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
    }
    
    try {
        $pdo = getDBConnection();
        
        // Verify token and get user
        $stmt = $pdo->prepare("
            SELECT u.*, us.expires_at 
            FROM users u 
            JOIN user_sessions us ON u.id = us.user_id 
            WHERE us.token = ? AND us.expires_at > ? AND u.status = 'active'
        ");
        $stmt->execute([$token, date('Y-m-d H:i:s')]);
        $user = $stmt->fetch();
        
        if (!$user) {
            sendJsonResponse(['success' => false, 'message' => 'Invalid or expired session'], 401);
        }
        
        return $user;
    } catch (Exception $e) {
        logError("Session verification error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Authentication failed'], 500);
    }
}

// Get user's Telegram access status and available channels
function getTelegramAccess() {
    $user = verifyUserSession();
    
    try {
        $pdo = getDBConnection();
        
        // Get user's current subscription
        $stmt = $pdo->prepare("
            SELECT us.*, sp.plan_type, sp.name as plan_name
            FROM user_subscriptions us
            JOIN subscription_plans sp ON us.plan_type = sp.plan_type
            WHERE us.user_id = ? AND us.status = 'active' AND us.end_date > ?
            ORDER BY us.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$user['id'], date('Y-m-d H:i:s')]);
        $subscription = $stmt->fetch();
        
        // Get all available Telegram channels
        $stmt = $pdo->prepare("SELECT * FROM telegram_channels WHERE is_active = TRUE ORDER BY plan_type, id");
        $stmt->execute();
        $allChannels = $stmt->fetchAll();
        
        // Get user's current Telegram access
        $stmt = $pdo->prepare("
            SELECT uta.*, tc.name as channel_name, tc.channel_id, tc.invite_link, tc.description
            FROM user_telegram_access uta
            JOIN telegram_channels tc ON uta.channel_id = tc.id
            WHERE uta.user_id = ? AND uta.status = 'active' AND uta.expires_at > ?
        ");
        $stmt->execute([$user['id'], date('Y-m-d H:i:s')]);
        $userAccess = $stmt->fetchAll();
        
        // Determine accessible channels based on subscription
        $accessibleChannels = [];
        $userPlanType = $subscription ? $subscription['plan_type'] : null;
        
        foreach ($allChannels as $channel) {
            $hasAccess = false;
            $accessReason = '';
            
            if (!$subscription) {
                $hasAccess = false;
                $accessReason = 'No active subscription';
            } elseif ($channel['plan_type'] === 'basic') {
                $hasAccess = in_array($userPlanType, ['basic', 'premium', 'vip']);
                $accessReason = $hasAccess ? 'Included in your plan' : 'Upgrade required';
            } elseif ($channel['plan_type'] === 'premium') {
                $hasAccess = in_array($userPlanType, ['premium', 'vip']);
                $accessReason = $hasAccess ? 'Included in your plan' : 'Premium or VIP plan required';
            } elseif ($channel['plan_type'] === 'vip') {
                $hasAccess = $userPlanType === 'vip';
                $accessReason = $hasAccess ? 'Included in your plan' : 'VIP plan required';
            }
            
            // Check if user is already joined
            $isJoined = false;
            foreach ($userAccess as $access) {
                if ($access['channel_id'] == $channel['id']) {
                    $isJoined = true;
                    break;
                }
            }
            
            $accessibleChannels[] = [
                'id' => $channel['id'],
                'name' => $channel['name'],
                'description' => $channel['description'],
                'plan_type' => $channel['plan_type'],
                'member_count' => $channel['member_count'],
                'has_access' => $hasAccess,
                'is_joined' => $isJoined,
                'access_reason' => $accessReason,
                'invite_link' => $hasAccess ? $channel['invite_link'] : null
            ];
        }
        
        sendJsonResponse([
            'success' => true,
            'subscription' => $subscription,
            'channels' => $accessibleChannels,
            'user_access' => $userAccess
        ]);
        
    } catch (Exception $e) {
        logError("Get Telegram access error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to load Telegram access'], 500);
    }
}

// Join a Telegram channel
function joinTelegramChannel() {
    $user = verifyUserSession();
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid input data'], 400);
    }
    
    $channelId = $input['channel_id'] ?? 0;
    
    try {
        $pdo = getDBConnection();
        
        // Get user's current subscription
        $stmt = $pdo->prepare("
            SELECT us.*, sp.plan_type
            FROM user_subscriptions us
            JOIN subscription_plans sp ON us.plan_type = sp.plan_type
            WHERE us.user_id = ? AND us.status = 'active' AND us.end_date > ?
            ORDER BY us.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$user['id'], date('Y-m-d H:i:s')]);
        $subscription = $stmt->fetch();
        
        if (!$subscription) {
            sendJsonResponse(['success' => false, 'message' => 'Active subscription required'], 403);
        }
        
        // Get channel details
        $stmt = $pdo->prepare("SELECT * FROM telegram_channels WHERE id = ? AND is_active = TRUE");
        $stmt->execute([$channelId]);
        $channel = $stmt->fetch();
        
        if (!$channel) {
            sendJsonResponse(['success' => false, 'message' => 'Channel not found'], 404);
        }
        
        // Check if user has access to this channel
        $userPlanType = $subscription['plan_type'];
        $hasAccess = false;
        
        if ($channel['plan_type'] === 'basic') {
            $hasAccess = in_array($userPlanType, ['basic', 'premium', 'vip']);
        } elseif ($channel['plan_type'] === 'premium') {
            $hasAccess = in_array($userPlanType, ['premium', 'vip']);
        } elseif ($channel['plan_type'] === 'vip') {
            $hasAccess = $userPlanType === 'vip';
        }
        
        if (!$hasAccess) {
            sendJsonResponse(['success' => false, 'message' => 'Your subscription plan does not include access to this channel'], 403);
        }
        
        // Check if user is already joined
        $stmt = $pdo->prepare("
            SELECT * FROM user_telegram_access 
            WHERE user_id = ? AND channel_id = ? AND status = 'active' AND expires_at > ?
        ");
        $stmt->execute([$user['id'], $channelId, date('Y-m-d H:i:s')]);
        $existingAccess = $stmt->fetch();
        
        if ($existingAccess) {
            sendJsonResponse([
                'success' => true,
                'message' => 'You already have access to this channel',
                'invite_link' => $channel['invite_link']
            ]);
        }
        
        // Create new access record
        $expiresAt = $subscription['end_date']; // Access expires with subscription
        
        $stmt = $pdo->prepare("
            INSERT INTO user_telegram_access (user_id, channel_id, subscription_id, status, joined_at, expires_at, created_at)
            VALUES (?, ?, ?, 'active', ?, ?, ?)
        ");
        $stmt->execute([
            $user['id'],
            $channelId,
            $subscription['id'],
            date('Y-m-d H:i:s'),
            $expiresAt,
            date('Y-m-d H:i:s')
        ]);
        
        // Update channel member count
        $stmt = $pdo->prepare("UPDATE telegram_channels SET member_count = member_count + 1 WHERE id = ?");
        $stmt->execute([$channelId]);
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Successfully joined the channel!',
            'invite_link' => $channel['invite_link'],
            'channel_name' => $channel['name']
        ]);
        
    } catch (Exception $e) {
        logError("Join Telegram channel error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to join channel'], 500);
    }
}

// Leave a Telegram channel
function leaveTelegramChannel() {
    $user = verifyUserSession();
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid input data'], 400);
    }
    
    $channelId = $input['channel_id'] ?? 0;
    
    try {
        $pdo = getDBConnection();
        
        // Update access status to revoked
        $stmt = $pdo->prepare("
            UPDATE user_telegram_access 
            SET status = 'revoked', updated_at = ?
            WHERE user_id = ? AND channel_id = ? AND status = 'active'
        ");
        $stmt->execute([date('Y-m-d H:i:s'), $user['id'], $channelId]);
        
        if ($stmt->rowCount() > 0) {
            // Update channel member count
            $stmt = $pdo->prepare("UPDATE telegram_channels SET member_count = GREATEST(member_count - 1, 0) WHERE id = ?");
            $stmt->execute([$channelId]);
            
            sendJsonResponse([
                'success' => true,
                'message' => 'Successfully left the channel'
            ]);
        } else {
            sendJsonResponse(['success' => false, 'message' => 'No active access found for this channel'], 404);
        }
        
    } catch (Exception $e) {
        logError("Leave Telegram channel error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to leave channel'], 500);
    }
}

// Handle different request methods and actions
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

switch ($method) {
    case 'GET':
        if ($action === 'access') {
            getTelegramAccess();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;
        
    case 'POST':
        if ($action === 'join') {
            joinTelegramChannel();
        } elseif ($action === 'leave') {
            leaveTelegramChannel();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;
        
    default:
        sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
        break;
}
?>
