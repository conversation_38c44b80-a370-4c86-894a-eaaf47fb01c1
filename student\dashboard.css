/* Student Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #f8fafc;
    color: #334155;
    line-height: 1.6;
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: #fff;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    overflow-y: auto;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.logo h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.logo span {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Navigation */
.sidebar-nav {
    padding: 1rem 0;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border-left-color: #fbbf24;
}

.nav-item.active .nav-link {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
    border-left-color: #fbbf24;
}

.nav-link i {
    width: 1.25rem;
    text-align: center;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout {
    color: #fca5a5 !important;
}

.logout:hover {
    background: rgba(239, 68, 68, 0.1) !important;
    border-left-color: #ef4444 !important;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    min-height: 100vh;
    background: #f8fafc;
}

/* Header */
.dashboard-header {
    background: #fff;
    padding: 1rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #64748b;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background 0.2s;
}

.sidebar-toggle:hover {
    background: #f1f5f9;
}

.page-title h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.page-title p {
    color: #64748b;
    font-size: 0.875rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.875rem;
}

.user-role {
    font-size: 0.75rem;
    color: #64748b;
}

/* Dashboard Content */
.dashboard-content {
    padding: 2rem;
}

.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: #fff;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.clickable {
    cursor: pointer;
    position: relative;
}

.stat-action {
    position: absolute;
    top: 1rem;
    right: 1rem;
    color: #9ca3af;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.stat-card.clickable:hover .stat-action {
    color: #4f46e5;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #fff;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
}

.stat-content h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: #64748b;
    font-size: 0.875rem;
}

/* Content Sections */
.content-section {
    background: #fff;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.section-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: #fff;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
}

.activity-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 0.875rem;
}

.activity-content h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.activity-content p {
    font-size: 0.75rem;
    color: #64748b;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 2rem;
}

.quick-actions h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-card {
    background: #fff;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #4f46e5;
}

.action-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #fff;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
}

.action-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.action-content p {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0;
}

/* Subscription Widget */
.subscription-status {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
}

.current-plan {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
}

.plan-info h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.plan-info p {
    color: #64748b;
    margin-bottom: 1rem;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    font-size: 0.875rem;
}

.plan-features .fa-check {
    color: #10b981;
}

.plan-features .fa-times {
    color: #ef4444;
}

.upgrade-prompt {
    text-align: center;
    padding: 2rem;
}

.upgrade-content h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.upgrade-content p {
    color: #64748b;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.btn-gradient {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: #fff;
    border: none;
    padding: 0.875rem 2rem;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-gradient:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.modal {
    background: #fff;
    border-radius: 1rem;
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideUp 0.3s ease-out;
}

@keyframes modalSlideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #64748b;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.modal-body {
    padding: 1.5rem;
}

.modal-step {
    display: none;
}

.modal-step.active {
    display: block;
}

/* Plan Selection */
.plans-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.plan-option {
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.2s ease;
    position: relative;
}

.plan-option:hover {
    border-color: #4f46e5;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
}

.plan-option.featured {
    border-color: #4f46e5;
    background: linear-gradient(135deg, #f8faff, #f1f5ff);
}

.plan-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: #fff;
    padding: 0.25rem 1rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.plan-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.plan-price {
    margin-bottom: 1.5rem;
}

.plan-price .price {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
}

.plan-price .period {
    color: #64748b;
    font-size: 0.875rem;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
    text-align: left;
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    font-size: 0.875rem;
}

.plan-features .fa-check {
    color: #10b981;
}

/* Payment Form */
.payment-summary {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 1rem;
}

.payment-summary h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.amount-display {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.5rem;
}

.amount-display .currency {
    font-size: 1.2rem;
    color: #64748b;
}

.amount-display .amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
}

.amount-display .period {
    color: #64748b;
}

.phone-input {
    display: flex;
    align-items: center;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    overflow: hidden;
    transition: border-color 0.2s ease;
}

.phone-input:focus-within {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.country-code {
    background: #f3f4f6;
    padding: 0.75rem;
    border-right: 1px solid #d1d5db;
    font-weight: 600;
    color: #374151;
}

.phone-input input {
    flex: 1;
    border: none;
    padding: 0.75rem;
    outline: none;
    font-size: 1rem;
}

.payment-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.payment-actions .btn {
    flex: 1;
}

/* Processing Animation */
.processing-animation {
    text-align: center;
    margin-bottom: 2rem;
}

.phone-icon {
    font-size: 4rem;
    color: #4f46e5;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

.processing-timer {
    background: #fef3c7;
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
    margin-top: 1rem;
    font-weight: 600;
}

/* Success/Error Animations */
.success-animation,
.error-animation {
    text-align: center;
    margin-bottom: 2rem;
}

.success-animation i {
    font-size: 4rem;
    color: #10b981;
    margin-bottom: 1rem;
}

.error-animation i {
    font-size: 4rem;
    color: #ef4444;
    margin-bottom: 1rem;
}

.success-actions,
.error-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar-toggle {
        display: block;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-header {
        padding: 1rem;
    }

    .subscription-status {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }

    .plans-showcase {
        grid-template-columns: 1fr;
    }

    .payment-actions {
        flex-direction: column;
    }

    .success-actions,
    .error-actions {
        flex-direction: column;
    }
}

/* Additional Styles for New Pages */

/* Courses Page Styles */
.progress-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.progress-card {
    background: #fff;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #fff;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
}

.progress-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.progress-info p {
    color: #64748b;
    margin: 0;
    font-size: 0.875rem;
}

.course-tabs {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tab-nav {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
}

.tab-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    cursor: pointer;
    font-weight: 500;
    color: #64748b;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.tab-btn.active {
    background: #fff;
    color: #4f46e5;
    border-bottom: 2px solid #4f46e5;
}

.tab-btn:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.tab-content {
    display: none;
    padding: 2rem;
}

.tab-content.active {
    display: block;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.course-card {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.2s ease;
}

.course-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.course-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.course-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #4f46e5;
    color: #fff;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.course-badge.completed {
    background: #10b981;
}

.course-price {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.course-content {
    padding: 1.5rem;
}

.course-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.course-content p {
    color: #64748b;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.course-progress {
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 0.5rem;
    background: #e2e8f0;
    border-radius: 0.25rem;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 0.25rem;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

.course-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    color: #64748b;
}

.course-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.course-completion {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #10b981;
    font-size: 0.875rem;
    font-weight: 500;
}

.course-actions {
    display: flex;
    gap: 0.75rem;
}

.course-actions .btn {
    flex: 1;
}

/* Signals Page Styles */
.signal-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.signal-stats .stat-card {
    background: #fff;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.signal-stats .stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #fff;
}

.signal-stats .stat-icon.green {
    background: linear-gradient(135deg, #10b981, #059669);
}

.signal-stats .stat-icon.blue {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.signal-stats .stat-icon.purple {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.signal-stats .stat-icon.orange {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.signal-filters {
    background: #fff;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.filter-group select {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background: #fff;
}

.signals-section {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
}

.section-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.signal-count {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

.signals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

.signal-card {
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.signal-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.signal-card.live {
    border-left: 4px solid #10b981;
}

.signal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.signal-pair {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.pair {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1e293b;
}

.signal-type {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.signal-type.buy {
    background: #dcfce7;
    color: #166534;
}

.signal-type.sell {
    background: #fee2e2;
    color: #991b1b;
}

.signal-status {
    text-align: right;
}

.status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status.active {
    background: #dcfce7;
    color: #166534;
}

.status.pending {
    background: #fef3c7;
    color: #92400e;
}

.status.closed {
    background: #f3f4f6;
    color: #374151;
}

.status.win {
    background: #dcfce7;
    color: #166534;
}

.status.loss {
    background: #fee2e2;
    color: #991b1b;
}

.time {
    display: block;
    font-size: 0.75rem;
    color: #64748b;
    margin-top: 0.25rem;
}

.signal-details {
    margin-bottom: 1rem;
}

.price-levels {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.level {
    text-align: center;
}

.level label {
    display: block;
    font-size: 0.75rem;
    color: #64748b;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.price {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
}

.signal-progress {
    margin-top: 1rem;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.profit {
    font-weight: 600;
}

.profit.positive {
    color: #10b981;
}

.profit.negative {
    color: #ef4444;
}

.signal-actions {
    display: flex;
    gap: 0.75rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.signals-table {
    padding: 1.5rem;
}

.table-header,
.table-row {
    display: grid;
    grid-template-columns: 1fr 0.8fr 0.8fr 0.8fr 0.8fr 0.8fr 1fr;
    gap: 1rem;
    padding: 0.75rem 0;
    align-items: center;
}

.table-header {
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
    padding: 1rem;
    margin: -1.5rem -1.5rem 0 -1.5rem;
}

.table-row {
    border-bottom: 1px solid #f1f5f9;
    font-size: 0.875rem;
}

.table-row:last-child {
    border-bottom: none;
}

.pair-name {
    font-weight: 600;
    color: #1e293b;
}

.pips {
    font-weight: 600;
}

.pips.positive {
    color: #10b981;
}

.pips.negative {
    color: #ef4444;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

.analysis-card {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
}

.analysis-card h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.75rem;
}

.analysis-card p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.analysis-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
}

.sentiment {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: 600;
    text-transform: uppercase;
}

.sentiment.bullish {
    background: #dcfce7;
    color: #166534;
}

.sentiment.bearish {
    background: #fee2e2;
    color: #991b1b;
}

.sentiment.neutral {
    background: #f3f4f6;
    color: #374151;
}

.confidence {
    color: #64748b;
    font-weight: 500;
}

.update-time {
    font-size: 0.875rem;
    color: #64748b;
}

.signal-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: #10b981;
}

.status-indicator.active {
    animation: pulse 2s infinite;
}

/* Telegram Page Styles */
.community-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.overview-card {
    background: #fff;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.card-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #fff;
    background: linear-gradient(135deg, #0088cc, #229ed9);
}

.card-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.card-content p {
    color: #64748b;
    margin: 0;
    font-size: 0.875rem;
}

.telegram-channels {
    background: #fff;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.telegram-channels h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1.5rem;
}

.channels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.channel-card {
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.channel-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.channel-card.featured {
    border-color: #0088cc;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
}

.channel-card.premium {
    border-color: #4f46e5;
    background: linear-gradient(135deg, #f8faff, #f1f5ff);
}

.channel-card.vip {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb, #fef3c7);
}

.channel-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.channel-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #fff;
    background: linear-gradient(135deg, #0088cc, #229ed9);
}

.channel-card.premium .channel-icon {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
}

.channel-card.vip .channel-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.channel-info h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.channel-info p {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0;
}

.channel-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: auto;
}

.channel-badge.free {
    background: #dcfce7;
    color: #166534;
}

.channel-badge.premium {
    background: #e0e7ff;
    color: #3730a3;
}

.channel-badge.vip {
    background: #fef3c7;
    color: #92400e;
}

.channel-description {
    margin-bottom: 1rem;
}

.channel-description p {
    color: #64748b;
    line-height: 1.5;
}

.channel-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #64748b;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.channel-features ul {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.channel-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    font-size: 0.875rem;
    color: #374151;
}

.channel-features .fa-check {
    color: #10b981;
}

.telegram-activity {
    background: #fff;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.activity-feed {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: #fff;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    flex-shrink: 0;
}

.activity-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.activity-content p {
    color: #64748b;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.activity-time {
    font-size: 0.75rem;
    color: #9ca3af;
}

.telegram-bot {
    background: #fff;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.bot-card {
    max-width: 500px;
}

.bot-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.bot-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #fff;
    background: linear-gradient(135deg, #10b981, #059669);
}

.bot-details h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.bot-details p {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0;
}

.bot-features h5 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.75rem;
}

.bot-features ul {
    list-style: none;
    padding: 0;
    margin-bottom: 1.5rem;
}

.bot-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    font-size: 0.875rem;
    color: #374151;
}

.bot-features .fas {
    color: #10b981;
    width: 1rem;
}

.telegram-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-right: 1rem;
}

.telegram-status .status-indicator {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: #10b981;
}

.telegram-status span {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

.error-message {
    text-align: center;
    padding: 2rem;
    color: #64748b;
    font-style: italic;
}

/* Enhanced Subscription Page Styles */
.subscription-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.current-subscription-status {
    margin-bottom: 2rem;
}

.status-card {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    padding: 2rem;
    transition: all 0.2s ease;
}

.status-card.active {
    border-color: #10b981;
    background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.status-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.status-info p {
    color: #64748b;
    margin: 0;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

.status-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
}

.detail-item .label {
    font-weight: 500;
    color: #64748b;
    font-size: 0.875rem;
}

.detail-item .value {
    font-weight: 600;
    color: #1e293b;
}

.status-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-telegram {
    background: linear-gradient(135deg, #0088cc, #229ed9);
    color: #fff;
    border: none;
    transition: all 0.2s ease;
}

.btn-telegram:hover {
    background: linear-gradient(135deg, #006699, #1a7db8);
    transform: translateY(-1px);
}

.no-subscription {
    text-align: center;
    padding: 3rem 2rem;
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.no-sub-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
}

.no-subscription h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.no-subscription p {
    color: #64748b;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
}

.available-plans {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    padding: 2rem;
}

.available-plans .section-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.available-plans .section-header h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.available-plans .section-header p {
    color: #64748b;
    font-size: 1.125rem;
    margin: 0;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.plan-card {
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.plan-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #4f46e5;
}

.plan-card.current {
    border-color: #10b981;
    background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
}

.plan-badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: #fff;
    padding: 0.5rem 2rem;
    border-radius: 0 0 1rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
}

.plan-header {
    margin-bottom: 1.5rem;
}

.plan-header h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
}

.plan-price {
    margin-bottom: 0.5rem;
}

.plan-price .price {
    font-size: 3rem;
    font-weight: 800;
    color: #1e293b;
    line-height: 1;
}

.plan-price .period {
    color: #64748b;
    font-size: 1.125rem;
    font-weight: 500;
}

.yearly-price {
    color: #10b981;
    font-size: 0.875rem;
    font-weight: 600;
}

.savings {
    color: #059669;
    font-weight: 700;
}

.plan-description {
    margin-bottom: 1.5rem;
}

.plan-description p {
    color: #64748b;
    line-height: 1.6;
}

.plan-features {
    margin-bottom: 2rem;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
    font-size: 0.875rem;
    color: #374151;
}

.plan-features li:last-child {
    border-bottom: none;
}

.plan-features .fa-check {
    color: #10b981;
    font-weight: 600;
}

.plan-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.plan-actions .btn {
    width: 100%;
    padding: 0.875rem 1.5rem;
    font-weight: 600;
    border-radius: 0.75rem;
    transition: all 0.2s ease;
}

.btn-current {
    background: #10b981;
    color: #fff;
    border: none;
    cursor: not-allowed;
    opacity: 0.8;
}

/* Enhanced Tab Styles for Profile */
.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    font-weight: 500;
    color: #64748b;
}

.tab-btn.active {
    color: #4f46e5;
    border-bottom-color: #4f46e5;
    background: rgba(79, 70, 229, 0.05);
}

.tab-btn:hover {
    color: #1e293b;
    background: #f8fafc;
}

.tab-btn i {
    font-size: 1rem;
}

/* Modal Enhancements */
.modal {
    max-width: 600px;
    width: 95%;
}

.payment-summary {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
}

.payment-summary h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.payment-summary p {
    color: #64748b;
    margin-bottom: 1.5rem;
}

.amount-display {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.amount-display .currency {
    font-size: 1.5rem;
    color: #64748b;
    font-weight: 600;
}

.amount-display .amount {
    font-size: 3rem;
    font-weight: 800;
    color: #1e293b;
    line-height: 1;
}

.amount-display .period {
    color: #64748b;
    font-size: 1.125rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.form-group small {
    color: #64748b;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: block;
}

.phone-input {
    display: flex;
    align-items: center;
    border: 2px solid #d1d5db;
    border-radius: 0.75rem;
    overflow: hidden;
    transition: border-color 0.2s ease;
    background: #fff;
}

.phone-input:focus-within {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.country-code {
    background: #f3f4f6;
    padding: 1rem;
    border-right: 1px solid #d1d5db;
    font-weight: 700;
    color: #374151;
    font-size: 1rem;
}

.phone-input input {
    flex: 1;
    border: none;
    padding: 1rem;
    outline: none;
    font-size: 1rem;
    font-weight: 500;
}

.payment-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.payment-actions .btn {
    flex: 1;
    padding: 1rem 1.5rem;
    font-weight: 600;
    border-radius: 0.75rem;
}

/* Processing Animations */
.processing-animation {
    text-align: center;
    margin-bottom: 2rem;
}

.phone-icon {
    font-size: 4rem;
    color: #4f46e5;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f4f6;
    border-top: 5px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

.processing-timer {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    padding: 1rem;
    border-radius: 0.75rem;
    text-align: center;
    margin-top: 1rem;
    font-weight: 600;
    color: #92400e;
    border: 1px solid #f59e0b;
}

.success-animation,
.error-animation {
    text-align: center;
    margin-bottom: 2rem;
}

.success-animation i {
    font-size: 5rem;
    color: #10b981;
    margin-bottom: 1rem;
}

.error-animation i {
    font-size: 5rem;
    color: #ef4444;
    margin-bottom: 1rem;
}

.success-actions,
.error-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.success-actions .btn,
.error-actions .btn {
    flex: 1;
    padding: 1rem 1.5rem;
    font-weight: 600;
    border-radius: 0.75rem;
}
