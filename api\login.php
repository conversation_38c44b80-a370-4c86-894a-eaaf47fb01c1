<?php
require_once 'config.php';

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($input['email']) || !isset($input['password'])) {
    sendJsonResponse(['success' => false, 'message' => 'Email and password are required'], 400);
}

$email = filter_var($input['email'], FILTER_SANITIZE_EMAIL);
$password = $input['password'];
$remember = isset($input['remember']) ? $input['remember'] : false;

// Validate email format
if (!validateEmail($email)) {
    sendJsonResponse(['success' => false, 'message' => 'Invalid email format'], 400);
}

try {
    // Connect to database
    $pdo = getDBConnection();
    
    // Check if user exists
    $stmt = $pdo->prepare("SELECT id, name, email, password, role, email_verified, status, created_at FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid email or password'], 401);
    }

    // Verify password
    if (!password_verify($password, $user['password'])) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid email or password'], 401);
    }

    // Check if email is verified (optional)
    if (!$user['email_verified']) {
        sendJsonResponse(['success' => false, 'message' => 'Please verify your email before logging in'], 403);
    }

    // Check if user account is active
    if ($user['status'] !== 'active') {
        sendJsonResponse(['success' => false, 'message' => 'Your account has been suspended. Please contact support.'], 403);
    }
    
    // Generate session token
    $token = generateToken();

    // Store session in database
    $expires_at = $remember ?
        date('Y-m-d H:i:s', time() + REMEMBER_LIFETIME) :
        date('Y-m-d H:i:s', time() + SESSION_LIFETIME);
    
    $stmt = $pdo->prepare("INSERT INTO user_sessions (user_id, token, expires_at) VALUES (?, ?, ?)");
    $stmt->execute([$user['id'], $token, $expires_at]);
    
    // Update last login
    $stmt = $pdo->prepare("UPDATE users SET last_login = ? WHERE id = ?");
    $stmt->execute([date('Y-m-d H:i:s'), $user['id']]);

    // Set session variables
    session_start();
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_name'] = $user['name'];
    $_SESSION['user_role'] = $user['role'];
    $_SESSION['login_time'] = time();

    // Return success response
    sendJsonResponse([
        'success' => true,
        'message' => 'Login successful',
        'user' => [
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'role' => $user['role'],
            'token' => $token
        ]
    ]);
    
} catch (PDOException $e) {
    logError("Login database error: " . $e->getMessage(), ['email' => $email]);
    sendJsonResponse(['success' => false, 'message' => 'Database error occurred'], 500);
} catch (Exception $e) {
    logError("Login error: " . $e->getMessage(), ['email' => $email]);
    sendJsonResponse(['success' => false, 'message' => 'An error occurred'], 500);
}
?>
