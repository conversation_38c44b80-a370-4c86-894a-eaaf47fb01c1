<?php
require_once 'config.php';

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

try {
    // Connect to database
    $pdo = getDBConnection();
    
    // Get all users (excluding password for security)
    $stmt = $pdo->prepare("SELECT id, name, email, phone, role, email_verified, phone_verified, status, created_at, last_login FROM users ORDER BY created_at DESC");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Return success response
    sendJsonResponse([
        'success' => true,
        'users' => $users,
        'count' => count($users)
    ]);
    
} catch (PDOException $e) {
    logError("Get users database error: " . $e->getMessage());
    sendJsonResponse(['success' => false, 'message' => 'Database error occurred'], 500);
} catch (Exception $e) {
    logError("Get users error: " . $e->getMessage());
    sendJsonResponse(['success' => false, 'message' => 'An error occurred'], 500);
}
?>
