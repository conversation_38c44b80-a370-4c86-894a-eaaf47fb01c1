// Initialize default users
function initializeDefaultUsers() {
    const users = [
        {
            id: 1,
            name: 'Admin User',
            email: '<EMAIL>',
            phone: '254712345678',
            password: 'admin123',
            role: 'admin',
            emailVerified: true,
            phoneVerified: true,
            status: 'active',
            createdAt: new Date().toISOString(),
            isActive: true
        }
    ];
    localStorage.setItem('users', JSON.stringify(users));
    console.log('Default users initialized:', users);
}

// Add this to window for debugging
window.resetUsers = function() {
    initializeDefaultUsers();
    alert('Users reset! Admin credentials: <EMAIL> / admin123');
};

document.addEventListener('DOMContentLoaded', function() {
    // Initialize default users if localStorage is empty
    const existingUsers = localStorage.getItem('users');
    if (!existingUsers || existingUsers === '[]') {
        initializeDefaultUsers();
    }

    // Enhanced Mobile Navigation
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-menu a');
    const navbar = document.querySelector('.navbar');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
            document.body.classList.toggle('nav-open');
        });

        // Close mobile menu when clicking on a link
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navMenu.classList.remove('active');
                hamburger.classList.remove('active');
                document.body.classList.remove('nav-open');
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
                navMenu.classList.remove('active');
                hamburger.classList.remove('active');
                document.body.classList.remove('nav-open');
            }
        });
    }

    // Enhanced Navbar Background on Scroll
    window.addEventListener('scroll', function() {
        if (navbar) {
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
                navbar.style.backdropFilter = 'blur(20px)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = 'none';
                navbar.style.backdropFilter = 'blur(10px)';
            }
        }
    });

    // Smooth Scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Responsive Image Loading
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));

    // Authentication Modal
    const authModal = document.getElementById('authModal');
    const authModalClose = document.getElementById('authModalClose');
    const loginBtn = document.getElementById('loginBtn');
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const authToggleLink = document.getElementById('authToggleLink');
    const authModalTitle = document.getElementById('authModalTitle');
    const authToggleText = document.getElementById('authToggleText');
    const authLoading = document.getElementById('authLoading');
    const authAlert = document.getElementById('authAlert');

    let isLoginMode = true;

    // Open Auth Modal
    if (loginBtn) {
        loginBtn.addEventListener('click', function() {
            authModal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            showLoginForm();
        });
    }

    // Close Auth Modal
    if (authModalClose) {
        authModalClose.addEventListener('click', closeAuthModal);
    }

    window.addEventListener('click', function(e) {
        if (e.target === authModal) {
            closeAuthModal();
        }
    });

    function closeAuthModal() {
        if (authModal) {
            authModal.style.display = 'none';
            document.body.style.overflow = 'auto';
            resetAuthForms();
        }
    }

    // Toggle between Login and Register
    if (authToggleLink) {
        authToggleLink.addEventListener('click', function(e) {
            e.preventDefault();
            if (isLoginMode) {
                showRegisterForm();
            } else {
                showLoginForm();
            }
        });
    }

    function showLoginForm() {
        isLoginMode = true;
        authModalTitle.textContent = 'Login to Your Account';
        loginForm.style.display = 'block';
        registerForm.style.display = 'none';
        authToggleText.innerHTML = 'Don\'t have an account? <a href="#" id="authToggleLink">Sign up here</a>';

        // Re-attach event listener to new toggle link
        const newToggleLink = document.getElementById('authToggleLink');
        if (newToggleLink) {
            newToggleLink.addEventListener('click', function(e) {
                e.preventDefault();
                showRegisterForm();
            });
        }
    }

    function showRegisterForm() {
        isLoginMode = false;
        authModalTitle.textContent = 'Create Your Account';
        loginForm.style.display = 'none';
        registerForm.style.display = 'block';
        authToggleText.innerHTML = 'Already have an account? <a href="#" id="authToggleLink">Login here</a>';

        // Re-attach event listener to new toggle link
        const newToggleLink = document.getElementById('authToggleLink');
        if (newToggleLink) {
            newToggleLink.addEventListener('click', function(e) {
                e.preventDefault();
                showLoginForm();
            });
        }
    }

    // Login Form Submission
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            if (!email || !password) {
                showAuthAlert('Please fill in all fields', 'error');
                return;
            }

            try {
                showAuthLoading(true);
                hideAuthAlert();

                // Send login request to API
                const response = await fetch('api/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        remember: rememberMe
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAuthAlert('Login successful! Redirecting to dashboard...', 'success');

                    // Store user data and token
                    localStorage.setItem('userInfo', JSON.stringify(data.user));
                    localStorage.setItem('userToken', data.user.token);
                    localStorage.setItem('currentUser', JSON.stringify(data.user)); // Keep for compatibility
                    localStorage.setItem('authToken', data.user.token); // Keep for compatibility

                    // Redirect based on user role
                    setTimeout(() => {
                        if (data.user.role === 'admin') {
                            window.location.href = '/Forex/admin/dashboard';
                        } else {
                            window.location.href = '/Forex/student/dashboard';
                        }
                    }, 1500);
                } else {
                    showAuthAlert(data.message || 'Invalid credentials. Please try again.', 'error');
                }

            } catch (error) {
                console.error('Login error:', error);
                showAuthAlert('Network error. Please try again.', 'error');
            } finally {
                showAuthLoading(false);
            }
        });
    }

    // Register Form Submission
    if (registerForm) {
        registerForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const phone = document.getElementById('registerPhone').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const agreeTerms = document.getElementById('agreeTerms').checked;

            // Validation
            if (!name || !email || !password || !confirmPassword) {
                showAuthAlert('Please fill in all required fields', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showAuthAlert('Passwords do not match', 'error');
                return;
            }

            if (password.length < 6) {
                showAuthAlert('Password must be at least 6 characters long', 'error');
                return;
            }

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showAuthAlert('Please enter a valid email address', 'error');
                return;
            }

            if (!agreeTerms) {
                showAuthAlert('Please agree to the Terms of Service and Privacy Policy', 'error');
                return;
            }

            // Validate phone number only if provided
            if (phone && !validatePhoneNumber(phone)) {
                showAuthAlert('Please enter a valid phone number in format 254XXXXXXXXX', 'error');
                return;
            }

            try {
                showAuthLoading(true);
                hideAuthAlert();

                // Send registration request to API
                const response = await fetch('api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: name,
                        email: email,
                        phone: phone,
                        password: password
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAuthAlert('Welcome to ForexClass! Your account has been created successfully. You can now login.', 'success');

                    // Switch to login form after successful registration
                    setTimeout(() => {
                        showLoginForm();
                        // Pre-fill email in login form
                        document.getElementById('loginEmail').value = email;
                    }, 2000);
                } else {
                    showAuthAlert(data.message || 'Registration failed. Please try again.', 'error');
                }

            } catch (error) {
                console.error('Registration error:', error);
                showAuthAlert('Network error. Please try again.', 'error');
            } finally {
                showAuthLoading(false);
            }
        });
    }

    // Phone number formatting for register form
    const registerPhone = document.getElementById('registerPhone');
    if (registerPhone) {
        registerPhone.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits

            // Auto-add 254 if user starts with 0 or 7
            if (value.startsWith('0')) {
                value = '254' + value.substring(1);
            } else if (value.startsWith('7') && value.length <= 9) {
                value = '254' + value;
            }

            e.target.value = value;
        });
    }

    // Auth utility functions
    function showAuthLoading(show) {
        if (authLoading && loginForm && registerForm) {
            if (show) {
                authLoading.style.display = 'block';
                loginForm.style.display = 'none';
                registerForm.style.display = 'none';
            } else {
                authLoading.style.display = 'none';
                if (isLoginMode) {
                    loginForm.style.display = 'block';
                } else {
                    registerForm.style.display = 'block';
                }
            }
        }
    }

    function showAuthAlert(message, type) {
        if (authAlert) {
            authAlert.textContent = message;
            authAlert.className = `auth-alert ${type}`;
            authAlert.style.display = 'block';

            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    hideAuthAlert();
                }, 5000);
            }
        }
    }

    function hideAuthAlert() {
        if (authAlert) {
            authAlert.style.display = 'none';
        }
    }

    function resetAuthForms() {
        if (loginForm) {
            loginForm.reset();
        }
        if (registerForm) {
            registerForm.reset();
        }
        hideAuthAlert();
        showAuthLoading(false);
        showLoginForm(); // Always show login form when modal opens
    }

    // M-Pesa Payment Integration
    const modal = document.getElementById('paymentModal');
    const closeBtn = document.querySelector('.close');
    const selectPlanButtons = document.querySelectorAll('.select-plan');
    const mpesaForm = document.getElementById('mpesaPaymentForm');
    const paymentLoading = document.getElementById('paymentLoading');
    const paymentAlert = document.getElementById('paymentAlert');
    const phoneInput = document.getElementById('paymentPhone');

    let selectedPlan = '';
    let selectedPrice = 0;

    // Plan Selection
    selectPlanButtons.forEach(button => {
        button.addEventListener('click', function() {
            selectedPlan = this.dataset.plan;
            selectedPrice = parseInt(this.dataset.price);
            
            // Update modal content
            document.getElementById('selectedPlan').textContent = selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1);
            document.getElementById('selectedAmount').textContent = `Ksh ${selectedPrice.toLocaleString()}`;
            
            // Show modal
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        });
    });

    // Close Modal
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }
    
    window.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });

    function closeModal() {
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
            resetPaymentForm();
        }
    }

    // Phone Number Formatting
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
            
            // Auto-add 254 if user starts with 0 or 7
            if (value.startsWith('0')) {
                value = '254' + value.substring(1);
            } else if (value.startsWith('7') && value.length <= 9) {
                value = '254' + value;
            }
            
            e.target.value = value;
        });
    }

    // M-Pesa Payment Form Submission
    if (mpesaForm) {
        mpesaForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const phoneNumber = phoneInput.value;
            
            // Validate phone number
            if (!validatePhoneNumber(phoneNumber)) {
                showPaymentAlert('Please enter a valid phone number in format 254XXXXXXXXX', 'error');
                return;
            }

            try {
                showPaymentLoading(true);
                hidePaymentAlert();

                // Updated API endpoint for PHP
                const response = await fetch('https://forexhub.quatromgt.co.ke/api/mpesa.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phoneNumber: phoneNumber,
                        amount: selectedPrice,
                        accountReference: `${selectedPlan.toUpperCase()}_PLAN`,
                        transactionDesc: `Payment for ${selectedPlan} plan subscription`
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showPaymentAlert(
                        `Payment request sent successfully! Please check your phone (${phoneNumber}) for M-Pesa prompt and enter your PIN to complete the payment.`,
                        'success'
                    );
                    
                    // Store checkout request ID for potential status checking
                    if (result.data && result.data.CheckoutRequestID) {
                        localStorage.setItem('lastCheckoutRequestID', result.data.CheckoutRequestID);
                        localStorage.setItem('lastPlan', selectedPlan);
                        localStorage.setItem('lastAmount', selectedPrice);
                    }
                    
                    // Reset form after successful submission
                    mpesaForm.reset();
                } else {
                    showPaymentAlert(result.message || 'Payment request failed. Please try again.', 'error');
                }

            } catch (error) {
                console.error('Payment error:', error);
                showPaymentAlert('Network error. Please check your connection and try again.', 'error');
            } finally {
                showPaymentLoading(false);
            }
        });
    }

    function validatePhoneNumber(phone) {
        const phoneRegex = /^254[0-9]{9}$/;
        return phoneRegex.test(phone);
    }

    function showPaymentLoading(show) {
        if (paymentLoading && mpesaForm) {
            if (show) {
                paymentLoading.style.display = 'block';
                mpesaForm.style.display = 'none';
            } else {
                paymentLoading.style.display = 'none';
                mpesaForm.style.display = 'block';
            }
        }
    }

    function showPaymentAlert(message, type) {
        if (paymentAlert) {
            paymentAlert.textContent = message;
            paymentAlert.className = `payment-alert ${type}`;
            paymentAlert.style.display = 'block';
            
            // Auto-hide success messages after 10 seconds
            if (type === 'success') {
                setTimeout(() => {
                    hidePaymentAlert();
                }, 10000);
            }
        }
    }

    function hidePaymentAlert() {
        if (paymentAlert) {
            paymentAlert.style.display = 'none';
        }
    }

    function resetPaymentForm() {
        if (mpesaForm) {
            mpesaForm.reset();
        }
        hidePaymentAlert();
        showPaymentLoading(false);
    }

    // Contact Form
    const contactForm = document.querySelector('.contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Thank you for your message! We will get back to you soon.');
            contactForm.reset();
        });
    }

    // Newsletter Subscription
    const newsletterForm = document.querySelector('.newsletter');
    if (newsletterForm) {
        const newsletterBtn = newsletterForm.querySelector('button');
        if (newsletterBtn) {
            newsletterBtn.addEventListener('click', function(e) {
                e.preventDefault();
                const email = newsletterForm.querySelector('input').value;
                if (email) {
                    alert('Thank you for subscribing to our newsletter!');
                    newsletterForm.querySelector('input').value = '';
                }
            });
        }
    }

    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.feature-card, .course-card, .pricing-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // Hero section button handlers
    const telegramBtn = document.querySelector('.telegram-btn');
    const browseCourses = document.querySelector('.hero-buttons .btn-secondary');
    const getStartedBtn = document.querySelector('.nav-buttons .btn-primary');

    if (telegramBtn) {
        telegramBtn.addEventListener('click', function() {
            // Replace with your actual Telegram channel link
            window.open('https://t.me/your_channel_name', '_blank');
        });
    }

    if (browseCourses) {
        browseCourses.addEventListener('click', function() {
            document.querySelector('#courses').scrollIntoView({
                behavior: 'smooth'
            });
        });
    }

    if (getStartedBtn) {
        getStartedBtn.addEventListener('click', function() {
            // Open auth modal for registration
            if (authModal) {
                authModal.style.display = 'block';
                document.body.style.overflow = 'hidden';
                showRegisterForm();
            }
        });
    }

    // Smooth scroll for scroll indicator
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            document.querySelector('#about').scrollIntoView({
                behavior: 'smooth'
            });
        });
    }
});
