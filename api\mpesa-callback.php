<?php
require_once 'config.php';

// Log all incoming requests for debugging
$input = file_get_contents('php://input');
logError("M-Pesa Callback received: " . $input);

// Respond to M-Pesa immediately
http_response_code(200);
echo json_encode(['ResultCode' => 0, 'ResultDesc' => 'Success']);

// Process the callback data
$data = json_decode($input, true);

if (!$data) {
    logError("Invalid JSON in M-Pesa callback");
    exit;
}

try {
    $pdo = getDBConnection();
    
    // Extract callback data
    $stkCallback = $data['Body']['stkCallback'] ?? null;
    
    if (!$stkCallback) {
        logError("No stkCallback in M-Pesa response");
        exit;
    }
    
    $merchantRequestId = $stkCallback['MerchantRequestID'] ?? '';
    $checkoutRequestId = $stkCallback['CheckoutRequestID'] ?? '';
    $resultCode = $stkCallback['ResultCode'] ?? '';
    $resultDesc = $stkCallback['ResultDesc'] ?? '';
    
    // Find the payment record
    $stmt = $pdo->prepare("
        SELECT mp.*, us.id as subscription_id, us.plan_type 
        FROM mpesa_payments mp
        LEFT JOIN user_subscriptions us ON mp.subscription_id = us.id
        WHERE mp.checkout_request_id = ?
    ");
    $stmt->execute([$checkoutRequestId]);
    $payment = $stmt->fetch();
    
    if (!$payment) {
        logError("Payment record not found for CheckoutRequestID: " . $checkoutRequestId);
        exit;
    }
    
    $pdo->beginTransaction();
    
    if ($resultCode == 0) {
        // Payment successful
        $callbackMetadata = $stkCallback['CallbackMetadata']['Item'] ?? [];
        
        $mpesaReceiptNumber = '';
        $transactionDate = '';
        $phoneNumber = '';
        $amount = 0;
        
        // Extract metadata
        foreach ($callbackMetadata as $item) {
            switch ($item['Name']) {
                case 'MpesaReceiptNumber':
                    $mpesaReceiptNumber = $item['Value'];
                    break;
                case 'TransactionDate':
                    $transactionDate = $item['Value'];
                    break;
                case 'PhoneNumber':
                    $phoneNumber = $item['Value'];
                    break;
                case 'Amount':
                    $amount = $item['Value'];
                    break;
            }
        }
        
        // Convert transaction date
        $completedAt = null;
        if ($transactionDate) {
            $completedAt = DateTime::createFromFormat('YmdHis', $transactionDate);
            $completedAt = $completedAt ? $completedAt->format('Y-m-d H:i:s') : date('Y-m-d H:i:s');
        } else {
            $completedAt = date('Y-m-d H:i:s');
        }
        
        // Update payment record
        $stmt = $pdo->prepare("
            UPDATE mpesa_payments 
            SET status = 'completed', 
                mpesa_receipt_number = ?, 
                completed_at = ?,
                result_code = ?,
                result_desc = ?
            WHERE checkout_request_id = ?
        ");
        $stmt->execute([
            $mpesaReceiptNumber,
            $completedAt,
            $resultCode,
            $resultDesc,
            $checkoutRequestId
        ]);
        
        // Activate subscription if payment was successful
        if ($payment['subscription_id']) {
            $stmt = $pdo->prepare("
                UPDATE user_subscriptions 
                SET status = 'active', 
                    payment_status = 'completed',
                    activated_at = ?
                WHERE id = ?
            ");
            $stmt->execute([$completedAt, $payment['subscription_id']]);
            
            // Create transaction record
            $stmt = $pdo->prepare("
                INSERT INTO transactions (
                    user_id, subscription_id, transaction_id, amount, currency, 
                    payment_method, status, mpesa_receipt_number, description, created_at
                ) VALUES (?, ?, ?, ?, 'KES', 'mpesa', 'completed', ?, ?, ?)
            ");
            $stmt->execute([
                $payment['user_id'],
                $payment['subscription_id'],
                $mpesaReceiptNumber,
                $amount,
                $mpesaReceiptNumber,
                "Subscription payment - " . ($payment['plan_type'] ?? 'Plan'),
                $completedAt
            ]);
            
            logError("Subscription activated for user " . $payment['user_id'] . " - Plan: " . $payment['plan_type']);
        }
        
        logError("Payment completed successfully: " . $mpesaReceiptNumber);
        
    } else {
        // Payment failed
        $stmt = $pdo->prepare("
            UPDATE mpesa_payments 
            SET status = 'failed',
                result_code = ?,
                result_desc = ?,
                completed_at = ?
            WHERE checkout_request_id = ?
        ");
        $stmt->execute([
            $resultCode,
            $resultDesc,
            date('Y-m-d H:i:s'),
            $checkoutRequestId
        ]);
        
        // Mark subscription as failed if it exists
        if ($payment['subscription_id']) {
            $stmt = $pdo->prepare("
                UPDATE user_subscriptions 
                SET status = 'failed',
                    payment_status = 'failed'
                WHERE id = ?
            ");
            $stmt->execute([$payment['subscription_id']]);
        }
        
        logError("Payment failed: " . $resultDesc . " (Code: " . $resultCode . ")");
    }
    
    $pdo->commit();
    
} catch (Exception $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    logError("M-Pesa callback processing error: " . $e->getMessage());
}
?>
