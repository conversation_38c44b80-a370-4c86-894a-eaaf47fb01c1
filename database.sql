-- Database setup for ForexClass authentication system
-- Create database (run this first)
CREATE DATABASE IF NOT EXISTS forex_class;
USE forex_class;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone <PERSON><PERSON><PERSON><PERSON>(20),
    password VARCHAR(255) NOT NULL,
    role ENUM('user', 'admin', 'moderator') DEFAULT 'user',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(64),
    profile_image VARCHAR(255),
    telegram_username VA<PERSON>HA<PERSON>(100),
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status)
);

-- User sessions table for managing login sessions
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);

-- User profiles table (optional - for additional user information)
CREATE TABLE IF NOT EXISTS user_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    avatar VARCHAR(255),
    bio TEXT,
    trading_experience ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    preferred_markets JSON, -- Store as JSON array: ["forex", "crypto", "stocks"]
    timezone VARCHAR(50) DEFAULT 'UTC',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- User subscriptions table (for tracking plan subscriptions)
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_type ENUM('basic', 'premium', 'vip') NOT NULL,
    status ENUM('active', 'cancelled', 'expired', 'pending') DEFAULT 'pending',
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'KES',
    start_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    end_date DATETIME NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'mpesa',
    transaction_id VARCHAR(100),
    auto_renew BOOLEAN DEFAULT TRUE,
    cancellation_reason TEXT,
    cancelled_at DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_plan_type (plan_type),
    INDEX idx_end_date (end_date),
    INDEX idx_transaction_id (transaction_id)
);

-- Subscription plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    plan_type ENUM('basic', 'premium', 'vip') NOT NULL UNIQUE,
    description TEXT,
    price_monthly DECIMAL(10, 2) NOT NULL,
    price_yearly DECIMAL(10, 2),
    currency VARCHAR(3) DEFAULT 'KES',
    features JSON,
    telegram_access BOOLEAN DEFAULT FALSE,
    course_access_level INT DEFAULT 1,
    signal_access BOOLEAN DEFAULT FALSE,
    support_level ENUM('basic', 'priority', 'premium') DEFAULT 'basic',
    max_devices INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_plan_type (plan_type),
    INDEX idx_is_active (is_active)
);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subscription_id INT,
    transaction_id VARCHAR(100) UNIQUE NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'KES',
    payment_method VARCHAR(50) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    gateway_response JSON,
    mpesa_receipt_number VARCHAR(100),
    phone_number VARCHAR(20),
    description TEXT,
    processed_at DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_status (status),
    INDEX idx_payment_method (payment_method),
    INDEX idx_created_at (created_at)
);

-- Telegram channels table
CREATE TABLE IF NOT EXISTS telegram_channels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    channel_id VARCHAR(100) UNIQUE NOT NULL,
    invite_link VARCHAR(255),
    plan_type ENUM('basic', 'premium', 'vip') NOT NULL,
    description TEXT,
    member_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_plan_type (plan_type),
    INDEX idx_is_active (is_active)
);

-- User telegram access table
CREATE TABLE IF NOT EXISTS user_telegram_access (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    channel_id INT NOT NULL,
    subscription_id INT NOT NULL,
    status ENUM('active', 'revoked', 'expired') DEFAULT 'active',
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (channel_id) REFERENCES telegram_channels(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_channel (user_id, channel_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
);

-- Trading signals table
CREATE TABLE IF NOT EXISTS trading_signals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    signal_type ENUM('buy', 'sell') NOT NULL,
    entry_price DECIMAL(10, 5) NOT NULL,
    stop_loss DECIMAL(10, 5),
    take_profit DECIMAL(10, 5),
    current_price DECIMAL(10, 5),
    pips_gained DECIMAL(8, 2) DEFAULT 0,
    status ENUM('active', 'closed', 'cancelled') DEFAULT 'active',
    plan_access ENUM('basic', 'premium', 'vip') DEFAULT 'basic',
    description TEXT,
    image_url VARCHAR(255),
    created_by INT NOT NULL,
    closed_at DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_symbol (symbol),
    INDEX idx_status (status),
    INDEX idx_plan_access (plan_access),
    INDEX idx_created_at (created_at)
);

-- Courses table
CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    thumbnail VARCHAR(255),
    video_url VARCHAR(255),
    duration_minutes INT DEFAULT 0,
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    plan_access ENUM('basic', 'premium', 'vip') DEFAULT 'basic',
    order_index INT DEFAULT 0,
    is_published BOOLEAN DEFAULT FALSE,
    view_count INT DEFAULT 0,
    created_by INT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_plan_access (plan_access),
    INDEX idx_difficulty_level (difficulty_level),
    INDEX idx_is_published (is_published),
    INDEX idx_order_index (order_index)
);

-- User course progress table
CREATE TABLE IF NOT EXISTS user_course_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    progress_percentage DECIMAL(5, 2) DEFAULT 0,
    completed BOOLEAN DEFAULT FALSE,
    last_watched_position INT DEFAULT 0,
    completed_at DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_course (user_id, course_id),
    INDEX idx_user_id (user_id),
    INDEX idx_completed (completed)
);

-- Insert default subscription plans
INSERT INTO subscription_plans (name, plan_type, description, price_monthly, price_yearly, features, telegram_access, course_access_level, signal_access, support_level, max_devices) VALUES
('Basic Plan', 'basic', 'Perfect for beginners starting their trading journey', 2500.00, 25000.00, '["Access to basic courses", "Email support", "Basic trading signals", "Community access"]', FALSE, 1, TRUE, 'basic', 1),
('Premium Plan', 'premium', 'Advanced features for serious traders', 5000.00, 50000.00, '["All basic features", "Premium courses", "Telegram signals", "Priority support", "Advanced analytics"]', TRUE, 2, TRUE, 'priority', 2),
('VIP Plan', 'vip', 'Complete access with personal mentorship', 10000.00, 100000.00, '["All premium features", "VIP courses", "Personal mentor", "1-on-1 sessions", "Custom strategies", "Unlimited devices"]', TRUE, 3, TRUE, 'premium', 999);

-- Insert default telegram channels
INSERT INTO telegram_channels (name, channel_id, invite_link, plan_type, description) VALUES
('ForexClass Premium Signals', '@forexclass_premium', 'https://t.me/forexclass_premium', 'premium', 'Premium trading signals and market analysis'),
('ForexClass VIP Signals', '@forexclass_vip', 'https://t.me/forexclass_vip', 'vip', 'Exclusive VIP signals with personal guidance'),
('ForexClass Community', '@forexclass_community', 'https://t.me/forexclass_community', 'basic', 'General trading discussions and basic signals');

-- Password reset tokens table
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);

-- Email verification tokens table (separate from users for better tracking)
CREATE TABLE IF NOT EXISTS email_verification_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);

-- Insert sample admin user (password: admin@123)
INSERT INTO users (name, email, phone, password, role, email_verified, status) VALUES
('Admin User', '<EMAIL>', '254700000000', '$2y$10$mth2BZ4tiS7Tn81Sjo5f3Odwe5lC2oQZZ/2/dwckNemKNWJ4eUISq', 'admin', TRUE, 'active');

-- Insert sample regular user (password: user123)
INSERT INTO users (name, email, phone, password, email_verified) VALUES 
('John Doe', '<EMAIL>', '254712345678', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', TRUE);

-- Create indexes for better performance
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_sessions_expires_at ON user_sessions(expires_at);

-- Clean up expired sessions (you can run this periodically)
-- DELETE FROM user_sessions WHERE expires_at < NOW();

-- Clean up expired password reset tokens (you can run this periodically)
-- DELETE FROM password_reset_tokens WHERE expires_at < NOW();

-- Clean up expired email verification tokens (you can run this periodically)
-- DELETE FROM email_verification_tokens WHERE expires_at < NOW();
