<?php
/**
 * Student Trading Signals Page
 * 
 * Displays trading signals and market analysis
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

session_start();
require_once '../api/config.php';

// Check if student is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'user') {
    header('Location: index.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Student';

$page_title = "Trading Signals - ForexClass Student Portal";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="user-id" content="<?php echo $user_id; ?>">
    <meta name="user-name" content="<?php echo htmlspecialchars($user_name); ?>">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <h2>ForexClass</h2>
                    <span>Student Portal</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="courses.php" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            <span>My Courses</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="subscription.php" class="nav-link">
                            <i class="fas fa-crown"></i>
                            <span>Subscription</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="profile.php" class="nav-link">
                            <i class="fas fa-user"></i>
                            <span>Profile</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="signals.php" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span>Trading Signals</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="telegram.php" class="nav-link">
                            <i class="fab fa-telegram"></i>
                            <span>Telegram</span>
                        </a>
                    </li>
                </ul>
                
                <div class="sidebar-footer">
                    <a href="../" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>Back to Website</span>
                    </a>
                    <a href="#logout" class="nav-link logout" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="dashboard-header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="page-title">
                        <h1>Trading Signals</h1>
                        <p>Professional forex trading signals and market analysis</p>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="signal-status">
                        <div class="status-indicator active"></div>
                        <span>Live Signals</span>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <span class="user-name"><?php echo htmlspecialchars($user_name); ?></span>
                            <span class="user-role">Student</span>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Signals Content -->
            <div class="dashboard-content">
                <!-- Signal Stats -->
                <div class="signal-stats">
                    <div class="stat-card">
                        <div class="stat-icon green">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="winRate">78%</h3>
                            <p>Win Rate</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon blue">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalSignals">156</h3>
                            <p>Total Signals</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon purple">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="avgPips">+45</h3>
                            <p>Avg Pips/Signal</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon orange">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="lastUpdate">2 min</h3>
                            <p>Last Update</p>
                        </div>
                    </div>
                </div>
                
                <!-- Signal Filters -->
                <div class="signal-filters">
                    <div class="filter-group">
                        <label>Currency Pair:</label>
                        <select id="pairFilter">
                            <option value="all">All Pairs</option>
                            <option value="EURUSD">EUR/USD</option>
                            <option value="GBPUSD">GBP/USD</option>
                            <option value="USDJPY">USD/JPY</option>
                            <option value="AUDUSD">AUD/USD</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>Signal Type:</label>
                        <select id="typeFilter">
                            <option value="all">All Types</option>
                            <option value="buy">Buy Signals</option>
                            <option value="sell">Sell Signals</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>Status:</label>
                        <select id="statusFilter">
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="closed">Closed</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                    
                    <button class="btn btn-primary" onclick="refreshSignals()">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
                
                <!-- Live Signals -->
                <div class="signals-section">
                    <div class="section-header">
                        <h3><i class="fas fa-broadcast-tower"></i> Live Signals</h3>
                        <div class="signal-count">
                            <span id="activeSignalsCount">3</span> Active Signals
                        </div>
                    </div>
                    
                    <div class="signals-grid" id="liveSignalsGrid">
                        <!-- Sample live signal -->
                        <div class="signal-card live">
                            <div class="signal-header">
                                <div class="signal-pair">
                                    <span class="pair">EUR/USD</span>
                                    <span class="signal-type buy">BUY</span>
                                </div>
                                <div class="signal-status">
                                    <span class="status active">ACTIVE</span>
                                    <span class="time">2 hours ago</span>
                                </div>
                            </div>
                            
                            <div class="signal-details">
                                <div class="price-levels">
                                    <div class="level">
                                        <label>Entry:</label>
                                        <span class="price">1.0850</span>
                                    </div>
                                    <div class="level">
                                        <label>Stop Loss:</label>
                                        <span class="price">1.0820</span>
                                    </div>
                                    <div class="level">
                                        <label>Take Profit:</label>
                                        <span class="price">1.0920</span>
                                    </div>
                                </div>
                                
                                <div class="signal-progress">
                                    <div class="progress-info">
                                        <span>Current: 1.0875</span>
                                        <span class="profit positive">+25 pips</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill positive" style="width: 35%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="signal-actions">
                                <button class="btn btn-sm btn-outline">
                                    <i class="fas fa-chart-line"></i>
                                    View Chart
                                </button>
                                <button class="btn btn-sm btn-primary">
                                    <i class="fas fa-copy"></i>
                                    Copy Signal
                                </button>
                            </div>
                        </div>
                        
                        <!-- More live signals will be loaded here -->
                    </div>
                </div>
                
                <!-- Recent Signals -->
                <div class="signals-section">
                    <div class="section-header">
                        <h3><i class="fas fa-history"></i> Recent Signals</h3>
                        <button class="btn btn-outline" onclick="viewAllSignals()">
                            View All
                        </button>
                    </div>
                    
                    <div class="signals-table" id="recentSignalsTable">
                        <div class="table-header">
                            <div class="col">Pair</div>
                            <div class="col">Type</div>
                            <div class="col">Entry</div>
                            <div class="col">Exit</div>
                            <div class="col">Pips</div>
                            <div class="col">Status</div>
                            <div class="col">Date</div>
                        </div>
                        
                        <!-- Sample recent signal -->
                        <div class="table-row">
                            <div class="col">
                                <span class="pair-name">GBP/USD</span>
                            </div>
                            <div class="col">
                                <span class="signal-type sell">SELL</span>
                            </div>
                            <div class="col">1.2650</div>
                            <div class="col">1.2580</div>
                            <div class="col">
                                <span class="pips positive">+70</span>
                            </div>
                            <div class="col">
                                <span class="status closed win">WIN</span>
                            </div>
                            <div class="col">Mar 20, 2024</div>
                        </div>
                        
                        <!-- More recent signals will be loaded here -->
                    </div>
                </div>
                
                <!-- Market Analysis -->
                <div class="signals-section">
                    <div class="section-header">
                        <h3><i class="fas fa-chart-area"></i> Market Analysis</h3>
                        <span class="update-time">Updated 5 minutes ago</span>
                    </div>
                    
                    <div class="analysis-grid">
                        <div class="analysis-card">
                            <h4>EUR/USD Analysis</h4>
                            <p>The EUR/USD pair is showing bullish momentum after breaking above the 1.0850 resistance level. We expect continued upward movement towards 1.0920.</p>
                            <div class="analysis-meta">
                                <span class="sentiment bullish">Bullish</span>
                                <span class="confidence">Confidence: 85%</span>
                            </div>
                        </div>
                        
                        <div class="analysis-card">
                            <h4>Market Outlook</h4>
                            <p>Today's economic calendar includes important USD data releases. Expect increased volatility during the New York session.</p>
                            <div class="analysis-meta">
                                <span class="sentiment neutral">Neutral</span>
                                <span class="confidence">Volatility: High</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="dashboard.js"></script>
    <script src="signals.js"></script>
</body>
</html>
