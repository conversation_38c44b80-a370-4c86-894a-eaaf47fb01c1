/* Modern Light Theme Admin Dashboard */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Theme Colors */
    --primary-color: #6366f1;
    --primary-light: #a5b4fc;
    --primary-dark: #4f46e5;
    --secondary-color: #f1f5f9;
    --accent-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-hover: #f8fafc;

    /* Text Colors */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --text-light: #94a3b8;

    /* Border Colors */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e1;
    --border-dark: #94a3b8;

    /* Shadow */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
    background: var(--bg-secondary);
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: #4f46e5;
    color: #fff;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    overflow-y: auto;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
}

.logo i {
    font-size: 1.5rem;
    color: #fbbf24;
}

.user-info {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.welcome-text {
    font-size: 0.875rem;
    opacity: 0.8;
}

.user-name {
    font-weight: 600;
    font-size: 1rem;
}

.sidebar-nav {
    padding: 1rem 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0 25px 25px 0;
    margin-right: 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    color: #fff;
    background: transparent;
}

.nav-link i {
    width: 20px;
    font-size: 1.1rem;
}

.nav-badge {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-left: auto;
}

.sidebar-footer {
    padding: 1rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout {
    color: #ef4444 !important;
}

.logout:hover {
    background: rgba(239, 68, 68, 0.1) !important;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    min-height: 100vh;
    background: #f8fafc;
}

/* Dashboard Header */
.dashboard-header {
    background: #fff;
    padding: 1rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #64748b;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background 0.3s ease;
}

.sidebar-toggle:hover {
    background: #f1f5f9;
}

.page-title h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.page-title p {
    font-size: 0.875rem;
    color: #64748b;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.exchange-rate {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f1f5f9;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
}

.rate-label {
    color: #64748b;
}

.rate-value {
    font-weight: 600;
    color: #1e293b;
}

.rate-refresh {
    color: #4f46e5;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.rate-refresh:hover {
    transform: rotate(180deg);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #64748b;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.notification-btn:hover {
    background: #f1f5f9;
    color: #4f46e5;
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #ef4444;
    color: #fff;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 50px;
    min-width: 1.25rem;
    text-align: center;
}

.user-avatar-small {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.user-avatar-small:hover {
    transform: scale(1.05);
}

/* Dashboard Content */
.dashboard-content {
    padding: 2rem;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.content-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.content-title i {
    font-size: 1.5rem;
    color: #4f46e5;
}

.content-title h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
}

.content-actions {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: #fff;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
}

.btn-secondary {
    background: #fff;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: #fff;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #fff;
}

.stat-icon.completed {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.stat-icon.pending {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-icon.failed {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-icon.total {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stat-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.stat-content p {
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.stat-change.positive {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.stat-change.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.stat-change.neutral {
    background: rgba(100, 116, 139, 0.1);
    color: #64748b;
}

/* Table Styles */
.table-container {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.table-title h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.table-count {
    font-size: 0.875rem;
    color: #64748b;
}

.table-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filter-select {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    background: #fff;
    color: #64748b;
    font-size: 0.875rem;
    cursor: pointer;
}

.search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    background: #fff;
    color: #64748b;
    font-size: 0.875rem;
    width: 250px;
}

.search-input:focus,
.filter-select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.table-wrapper {
    overflow-x: auto;
}

.transactions-table {
    width: 100%;
    border-collapse: collapse;
}

.transactions-table th {
    background: #f8fafc;
    padding: 1rem 1.5rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.875rem;
    color: #475569;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #e2e8f0;
}

.transactions-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.transactions-table tr:hover {
    background: #f8fafc;
}

.transaction-info {
    display: flex;
    flex-direction: column;
}

.transaction-id {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.875rem;
}

.transaction-date {
    font-size: 0.75rem;
    color: #64748b;
    margin-top: 0.25rem;
}

.user-info-cell {
    display: flex;
    flex-direction: column;
}

.user-info-cell .user-name {
    font-weight: 500;
    color: #1e293b;
    font-size: 0.875rem;
}

.user-info-cell .user-email {
    font-size: 0.75rem;
    color: #64748b;
    margin-top: 0.25rem;
}

.amount-info {
    display: flex;
    flex-direction: column;
}

.amount-ksh {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.875rem;
}

.amount-usd {
    font-size: 0.75rem;
    color: #64748b;
    margin-top: 0.25rem;
}

.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: lowercase;
}

.status-badge.completed {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.status-badge.failed {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-action {
    padding: 0.375rem 0.75rem;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-action.approve {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.btn-action.approve:hover {
    background: #16a34a;
    color: #fff;
}

.btn-action.reject {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.btn-action.reject:hover {
    background: #dc2626;
    color: #fff;
}

/* Pagination */
.table-pagination {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination-info {
    font-size: 0.875rem;
    color: #64748b;
}

.pagination-controls {
    display: flex;
    gap: 0.5rem;
}

.pagination-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    background: #fff;
    color: #64748b;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.pagination-btn.active {
    background: #4f46e5;
    color: #fff;
    border-color: #4f46e5;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Dashboard Overview Styles */
.quick-actions-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.action-card-dash {
    background: #fff;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
    border: 1px solid #e2e8f0;
}

.action-card-dash:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.action-icon-dash {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: #fff;
    font-size: 2rem;
}

.action-card-dash h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.action-card-dash p {
    color: #64748b;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.action-card-dash .btn {
    width: 100%;
    justify-content: center;
}

/* Subscription Management Styles */
.plan-distribution {
    background: #fff;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1.5rem;
}

.plan-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.plan-stat {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background: #f8fafc;
}

.plan-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.plan-stat.basic .plan-color {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.plan-stat.premium .plan-color {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.plan-stat.vip .plan-color {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.plan-info {
    display: flex;
    flex-direction: column;
}

.plan-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.95rem;
}

.plan-count {
    font-size: 0.875rem;
    color: #64748b;
}

/* Plan Badges */
.plan-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.plan-badge.basic {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
}

.plan-badge.premium {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.plan-badge.vip {
    background: rgba(139, 92, 246, 0.1);
    color: #7c3aed;
}

/* Status Badges for Subscriptions */
.status-badge.active {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.status-badge.expired {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.status-badge.cancelled {
    background: rgba(100, 116, 139, 0.1);
    color: #64748b;
}

/* Date Text */
.date-text {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

/* Subscription Action Buttons */
.btn-action.extend {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
}

.btn-action.extend:hover {
    background: #2563eb;
    color: #fff;
}

.btn-action.cancel {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.btn-action.cancel:hover {
    background: #dc2626;
    color: #fff;
}

.btn-action.renew {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.btn-action.renew:hover {
    background: #16a34a;
    color: #fff;
}

.btn-action.upgrade {
    background: rgba(139, 92, 246, 0.1);
    color: #7c3aed;
}

.btn-action.upgrade:hover {
    background: #7c3aed;
    color: #fff;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    animation: 0.3s modalSlideIn;
    max-height: 90vh;
    overflow-y: auto;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    background: #fff;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #1e293b;
    font-size: 1.3rem;
    font-weight: 600;
}

.close {
    color: #64748b;
    font-size: 28px;
    font-weight: 700;
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
}

.close:hover {
    color: #1e293b;
}

.modal-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: #fff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
}

.form-group label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.form-actions .btn {
    min-width: 120px;
}

/* Dashboard Content Grid */
.dashboard-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.dashboard-card .card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
}

.dashboard-card .card-header h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.dashboard-card .card-header p {
    margin: 0;
    color: #64748b;
    font-size: 0.875rem;
}

.dashboard-card .card-content {
    padding: 1.5rem;
}

/* Subscription Breakdown Styles */
.subscription-breakdown {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.subscription-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
}

.subscription-info {
    flex: 1;
    min-width: 0;
}

.plan-name {
    font-weight: 600;
    color: #1e293b;
    display: block;
    margin-bottom: 0.25rem;
}

.plan-count {
    font-size: 0.875rem;
    color: #64748b;
}

.subscription-bar {
    flex: 2;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.bar-fill.basic {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.bar-fill.premium {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.bar-fill.vip {
    background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.subscription-percentage {
    font-weight: 600;
    color: #1e293b;
    min-width: 3rem;
    text-align: right;
}

/* Transaction Table Styles */
.transaction-id {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.875rem;
}

.transaction-date {
    font-size: 0.75rem;
    color: #64748b;
    margin-top: 0.25rem;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 500;
    color: #1e293b;
    font-size: 0.875rem;
}

.user-email {
    font-size: 0.75rem;
    color: #64748b;
    margin-top: 0.25rem;
}

.plan-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.plan-badge.basic {
    background: #dbeafe;
    color: #1e40af;
}

.plan-badge.premium {
    background: #fef3c7;
    color: #92400e;
}

.plan-badge.vip {
    background: #f3e8ff;
    color: #7c2d12;
}

.plan-badge.unknown {
    background: #f1f5f9;
    color: #64748b;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 0.375rem;
}

.btn-sm i {
    font-size: 0.75rem;
}

/* Package Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 1rem;
}

.modal {
    background: #fff;
    border-radius: 1rem;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
    border-radius: 1rem 1rem 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.modal-close {
    background: none;
    border: none;
    color: #64748b;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s;
}

.modal-close:hover {
    background: #e2e8f0;
    color: #1e293b;
}

.modal-body {
    padding: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group small {
    display: block;
    margin-top: 0.25rem;
    color: #6b7280;
    font-size: 0.75rem;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-content-grid {
        grid-template-columns: 1fr;
    }

    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar-toggle {
        display: block;
    }

    .dashboard-header {
        padding: 1rem;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .table-filters {
        width: 100%;
        flex-wrap: wrap;
    }

    .search-input {
        width: 100%;
    }

    .table-pagination {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .exchange-rate {
        display: none;
    }

    .header-right {
        gap: 0.5rem;
    }

    .stats-grid {
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .transactions-table th,
    .transactions-table td {
        padding: 0.75rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .btn-action {
        font-size: 0.6875rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: 0.75rem;
    }

    .page-title h1 {
        font-size: 1.25rem;
    }

    .content-title h2 {
        font-size: 1.5rem;
    }

    .content-actions {
        width: 100%;
    }

    .btn {
        flex: 1;
        justify-content: center;
    }

    .table-container {
        border-radius: 0.5rem;
    }

    .table-header {
        padding: 1rem;
    }

    .transactions-table {
        font-size: 0.75rem;
    }

    .transactions-table th,
    .transactions-table td {
        padding: 0.5rem;
    }

    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Enhanced Plan Management Styles */
.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.plan-card {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    padding: 2rem;
    transition: all 0.2s ease;
}

.plan-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.plan-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.plan-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

.status-badge.inactive {
    background: #fee2e2;
    color: #991b1b;
}

.plan-pricing {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.5rem;
}

.price-item {
    flex: 1;
    text-align: center;
}

.price-item .price {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: #4f46e5;
}

.price-item .period {
    font-size: 0.875rem;
    color: #64748b;
}

.plan-features {
    margin-bottom: 1.5rem;
}

.plan-features p {
    color: #64748b;
    margin-bottom: 1rem;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.feature-item i {
    color: #10b981;
    font-size: 0.75rem;
}

.plan-access {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f1f5f9;
    border-radius: 0.5rem;
}

.access-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.access-item:last-child {
    margin-bottom: 0;
}

.access-label {
    font-size: 0.875rem;
    color: #64748b;
}

.access-value {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.access-value.enabled {
    background: #dcfce7;
    color: #166534;
}

.access-value.disabled {
    background: #fee2e2;
    color: #991b1b;
}

.plan-actions {
    display: flex;
    gap: 0.75rem;
}

.plan-actions .btn {
    flex: 1;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
}

/* Enhanced Modal Styles */
.modal {
    max-width: 600px;
    width: 95%;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    font-size: 0.875rem;
}

/* Enhanced Table Styles */
.transactions-table {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.table-header {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem 1.5rem;
    align-items: center;
    border-bottom: 1px solid #f1f5f9;
}

.table-row:last-child {
    border-bottom: none;
}

.table-header .table-row {
    font-weight: 600;
    color: #374151;
}

.table-cell {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 500;
    color: #1e293b;
}

.user-email {
    font-size: 0.875rem;
    color: #64748b;
}

.plan-name {
    font-weight: 500;
    color: #4f46e5;
}

.amount {
    font-weight: 600;
    color: #1e293b;
}

.status-badge.completed {
    background: #dcfce7;
    color: #166534;
}

.status-badge.pending {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.failed {
    background: #fee2e2;
    color: #991b1b;
}

.date {
    color: #64748b;
    font-size: 0.875rem;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #64748b;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #cbd5e1;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .plans-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .table-cell {
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f1f5f9;
    }

    .table-cell:last-child {
        border-bottom: none;
    }

    .plan-actions {
        flex-direction: column;
    }
}
