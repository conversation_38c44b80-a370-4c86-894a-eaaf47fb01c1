// Student Courses JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeCourses();
});

function initializeCourses() {
    initializeTabs();
    loadCourseData();
    initializeLogout();
}

function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // Load data for the selected tab
            loadTabData(targetTab);
        });
    });
}

function loadCourseData() {
    // Load initial data for enrolled courses
    loadTabData('enrolled');
    updateProgressStats();
}

function loadTabData(tabType) {
    switch(tabType) {
        case 'enrolled':
            loadEnrolledCourses();
            break;
        case 'available':
            loadAvailableCourses();
            break;
        case 'completed':
            loadCompletedCourses();
            break;
    }
}

async function loadEnrolledCourses() {
    const grid = document.getElementById('enrolledCoursesGrid');
    
    try {
        // Mock data - replace with actual API call
        const enrolledCourses = [
            {
                id: 1,
                title: 'Forex Trading Fundamentals',
                description: 'Learn the basics of forex trading, market analysis, and risk management.',
                progress: 65,
                totalHours: 8,
                totalLessons: 12,
                image: 'https://via.placeholder.com/300x200/4f46e5/ffffff?text=Forex+Basics',
                status: 'in_progress'
            },
            {
                id: 2,
                title: 'Technical Analysis Mastery',
                description: 'Master chart patterns, indicators, and technical analysis techniques.',
                progress: 30,
                totalHours: 10,
                totalLessons: 15,
                image: 'https://via.placeholder.com/300x200/7c3aed/ffffff?text=Technical+Analysis',
                status: 'in_progress'
            },
            {
                id: 3,
                title: 'Risk Management Strategies',
                description: 'Learn professional risk management and money management techniques.',
                progress: 80,
                totalHours: 6,
                totalLessons: 8,
                image: 'https://via.placeholder.com/300x200/059669/ffffff?text=Risk+Management',
                status: 'in_progress'
            }
        ];
        
        let coursesHTML = '';
        
        enrolledCourses.forEach(course => {
            coursesHTML += `
                <div class="course-card enrolled">
                    <div class="course-image">
                        <img src="${course.image}" alt="${course.title}">
                        <div class="course-badge">${getStatusBadge(course.status)}</div>
                    </div>
                    <div class="course-content">
                        <h3>${course.title}</h3>
                        <p>${course.description}</p>
                        <div class="course-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${course.progress}%"></div>
                            </div>
                            <span class="progress-text">${course.progress}% Complete</span>
                        </div>
                        <div class="course-meta">
                            <span><i class="fas fa-clock"></i> ${course.totalHours} hours</span>
                            <span><i class="fas fa-play"></i> ${course.totalLessons} lessons</span>
                        </div>
                        <button class="btn btn-primary" onclick="continueCourse(${course.id})">
                            <i class="fas fa-play"></i>
                            Continue Learning
                        </button>
                    </div>
                </div>
            `;
        });
        
        grid.innerHTML = coursesHTML;
        
    } catch (error) {
        console.error('Error loading enrolled courses:', error);
        grid.innerHTML = '<div class="error-message">Failed to load courses. Please try again.</div>';
    }
}

async function loadAvailableCourses() {
    const grid = document.getElementById('availableCoursesGrid');
    
    try {
        // Mock data - replace with actual API call
        const availableCourses = [
            {
                id: 4,
                title: 'Advanced Trading Strategies',
                description: 'Master advanced trading techniques and professional strategies.',
                price: 2500,
                totalHours: 12,
                totalLessons: 20,
                rating: 4.8,
                image: 'https://via.placeholder.com/300x200/10b981/ffffff?text=Advanced+Trading',
                isPremium: true
            },
            {
                id: 5,
                title: 'Algorithmic Trading',
                description: 'Learn to build and deploy automated trading systems.',
                price: 5000,
                totalHours: 15,
                totalLessons: 25,
                rating: 4.9,
                image: 'https://via.placeholder.com/300x200/f59e0b/ffffff?text=Algo+Trading',
                isPremium: true
            },
            {
                id: 6,
                title: 'Psychology of Trading',
                description: 'Master the mental aspects of successful trading.',
                price: 1500,
                totalHours: 6,
                totalLessons: 10,
                rating: 4.7,
                image: 'https://via.placeholder.com/300x200/ef4444/ffffff?text=Trading+Psychology',
                isPremium: false
            }
        ];
        
        let coursesHTML = '';
        
        availableCourses.forEach(course => {
            coursesHTML += `
                <div class="course-card available">
                    <div class="course-image">
                        <img src="${course.image}" alt="${course.title}">
                        <div class="course-price">KSH ${course.price.toLocaleString()}</div>
                    </div>
                    <div class="course-content">
                        <h3>${course.title}</h3>
                        <p>${course.description}</p>
                        <div class="course-meta">
                            <span><i class="fas fa-clock"></i> ${course.totalHours} hours</span>
                            <span><i class="fas fa-play"></i> ${course.totalLessons} lessons</span>
                            <span><i class="fas fa-star"></i> ${course.rating} rating</span>
                        </div>
                        <button class="btn btn-primary" onclick="enrollCourse(${course.id}, ${course.price})">
                            <i class="fas fa-shopping-cart"></i>
                            Enroll Now
                        </button>
                    </div>
                </div>
            `;
        });
        
        grid.innerHTML = coursesHTML;
        
    } catch (error) {
        console.error('Error loading available courses:', error);
        grid.innerHTML = '<div class="error-message">Failed to load courses. Please try again.</div>';
    }
}

async function loadCompletedCourses() {
    const grid = document.getElementById('completedCoursesGrid');
    
    try {
        // Mock data - replace with actual API call
        const completedCourses = [
            {
                id: 7,
                title: 'Market Analysis Techniques',
                description: 'Technical and fundamental analysis for forex markets.',
                completedDate: '2024-03-15',
                totalHours: 6,
                certificateId: 'CERT_001',
                image: 'https://via.placeholder.com/300x200/059669/ffffff?text=Market+Analysis'
            },
            {
                id: 8,
                title: 'Currency Pairs Overview',
                description: 'Understanding major, minor, and exotic currency pairs.',
                completedDate: '2024-02-28',
                totalHours: 4,
                certificateId: 'CERT_002',
                image: 'https://via.placeholder.com/300x200/7c3aed/ffffff?text=Currency+Pairs'
            }
        ];
        
        let coursesHTML = '';
        
        completedCourses.forEach(course => {
            const completedDate = new Date(course.completedDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            coursesHTML += `
                <div class="course-card completed">
                    <div class="course-image">
                        <img src="${course.image}" alt="${course.title}">
                        <div class="course-badge completed">Completed</div>
                    </div>
                    <div class="course-content">
                        <h3>${course.title}</h3>
                        <p>${course.description}</p>
                        <div class="course-completion">
                            <i class="fas fa-check-circle"></i>
                            <span>Completed on ${completedDate}</span>
                        </div>
                        <div class="course-meta">
                            <span><i class="fas fa-clock"></i> ${course.totalHours} hours</span>
                            <span><i class="fas fa-certificate"></i> Certificate earned</span>
                        </div>
                        <div class="course-actions">
                            <button class="btn btn-outline" onclick="reviewCourse(${course.id})">
                                <i class="fas fa-redo"></i>
                                Review Course
                            </button>
                            <button class="btn btn-primary" onclick="downloadCertificate('${course.certificateId}')">
                                <i class="fas fa-download"></i>
                                Download Certificate
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        grid.innerHTML = coursesHTML;
        
    } catch (error) {
        console.error('Error loading completed courses:', error);
        grid.innerHTML = '<div class="error-message">Failed to load courses. Please try again.</div>';
    }
}

function updateProgressStats() {
    // Mock data - replace with actual calculations
    document.getElementById('enrolledCourses').textContent = '5';
    document.getElementById('completedCourses').textContent = '2';
    document.getElementById('totalHours').textContent = '24';
    document.getElementById('averageProgress').textContent = '68%';
}

function getStatusBadge(status) {
    switch(status) {
        case 'in_progress':
            return 'In Progress';
        case 'completed':
            return 'Completed';
        case 'not_started':
            return 'Not Started';
        default:
            return 'Unknown';
    }
}

function continueCourse(courseId) {
    // Redirect to course player or learning interface
    alert(`Continuing course ${courseId}. This will redirect to the course player.`);
    // window.location.href = `../course-player.php?id=${courseId}`;
}

function enrollCourse(courseId, price) {
    // Show enrollment modal or redirect to payment
    if (confirm(`Enroll in this course for KSH ${price.toLocaleString()}?`)) {
        alert(`Enrolling in course ${courseId}. This will redirect to payment.`);
        // window.location.href = `../enroll.php?course=${courseId}`;
    }
}

function reviewCourse(courseId) {
    // Redirect to course review
    alert(`Reviewing course ${courseId}. This will redirect to the course content.`);
    // window.location.href = `../course-review.php?id=${courseId}`;
}

function downloadCertificate(certificateId) {
    // Download certificate
    alert(`Downloading certificate ${certificateId}. This will start the download.`);
    // window.open(`../api/certificate.php?id=${certificateId}`, '_blank');
}

function initializeLogout() {
    const logoutBtn = document.getElementById('logoutBtn');
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (confirm('Are you sure you want to logout?')) {
                // Clear user data
                localStorage.removeItem('userInfo');
                localStorage.removeItem('userToken');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('authToken');
                
                // Call logout API to destroy session
                fetch('../api/logout.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(() => {
                    // Redirect to home page
                    window.location.href = '../';
                }).catch(() => {
                    // Redirect anyway
                    window.location.href = '../';
                });
            }
        });
    }
}
