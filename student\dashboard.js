// Student Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeStudentDashboard();
});

function initializeStudentDashboard() {
    // Get user info from meta tags (set by PHP) or localStorage
    const userNameMeta = document.querySelector('meta[name="user-name"]');
    const userEmailMeta = document.querySelector('meta[name="user-email"]');
    const userIdMeta = document.querySelector('meta[name="user-id"]');

    if (userNameMeta && userEmailMeta && userIdMeta) {
        const user = {
            id: userIdMeta.content,
            name: userNameMeta.content,
            email: userEmailMeta.content,
            role: 'user'
        };
        updateUserInfo(user);
    } else {
        // Fallback to localStorage
        const userInfo = localStorage.getItem('userInfo');
        if (userInfo) {
            const user = JSON.parse(userInfo);
            updateUserInfo(user);
        }
    }

    // Initialize navigation
    initializeNavigation();
    
    // Initialize sidebar toggle
    initializeSidebarToggle();
    
    // Load dashboard data
    loadDashboardData();
    
    // Initialize logout
    initializeLogout();
}

function updateUserInfo(user) {
    const userNameElement = document.getElementById('userName');
    if (userNameElement) {
        userNameElement.textContent = user.name || 'Student';
    }
}

function initializeNavigation() {
    // Navigation is now handled by direct page links
    // No need to prevent default or handle page switching
    // Each page is a separate PHP file
}

// Old page navigation functions removed
// Navigation now handled by direct page links

// loadPageData function removed - each page loads its own data

function loadDashboardData() {
    loadDashboardStats();
    loadRecentActivity();
}

function loadDashboardStats() {
    // Load real user data
    loadUserSubscription();

    // Mock data for other stats - replace with actual API calls
    const stats = {
        totalCourses: 5,
        completedCourses: 2,
        studyHours: 24
    };

    document.getElementById('totalCourses').textContent = stats.totalCourses;
    document.getElementById('completedCourses').textContent = stats.completedCourses;
    document.getElementById('studyHours').textContent = stats.studyHours;
}

async function loadUserSubscription() {
    try {
        const userIdMeta = document.querySelector('meta[name="user-id"]');
        const userId = userIdMeta ? userIdMeta.content : null;

        if (!userId) return;

        const response = await fetch('../api/subscription.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_current_subscription',
                user_id: userId
            })
        });

        const data = await response.json();

        if (data.success && data.subscription) {
            const subscription = data.subscription;

            // Update subscription status
            document.getElementById('subscriptionStatus').textContent = subscription.plan_name;

            // Update subscription widget
            updateSubscriptionWidget(subscription);

            // Hide upgrade prompt if user has active subscription
            const upgradePrompt = document.getElementById('upgradePrompt');
            if (upgradePrompt) {
                upgradePrompt.style.display = 'none';
            }
        } else {
            // User has no active subscription
            document.getElementById('subscriptionStatus').textContent = 'Free';
            updateSubscriptionWidget(null);
        }

    } catch (error) {
        console.error('Error loading subscription:', error);
        document.getElementById('subscriptionStatus').textContent = 'Free';
    }
}

function updateSubscriptionWidget(subscription) {
    const currentPlanName = document.getElementById('currentPlanName');
    const currentPlanDescription = document.getElementById('currentPlanDescription');
    const currentPlanFeatures = document.getElementById('currentPlanFeatures');

    if (subscription) {
        currentPlanName.textContent = subscription.plan_name;
        currentPlanDescription.textContent = subscription.description || 'Premium access to forex education';

        // Update features based on subscription
        const features = JSON.parse(subscription.features || '[]');
        let featuresHTML = '';

        features.forEach(feature => {
            featuresHTML += `<li><i class="fas fa-check"></i> ${feature}</li>`;
        });

        // Add additional premium features
        if (subscription.telegram_access) {
            featuresHTML += `<li><i class="fas fa-check"></i> Telegram community access</li>`;
        }
        if (subscription.signal_access) {
            featuresHTML += `<li><i class="fas fa-check"></i> Premium trading signals</li>`;
        }

        currentPlanFeatures.innerHTML = featuresHTML;
    } else {
        // Free plan
        currentPlanName.textContent = 'Free Plan';
        currentPlanDescription.textContent = 'Basic access to forex education';
        currentPlanFeatures.innerHTML = `
            <li><i class="fas fa-check"></i> Basic courses access</li>
            <li><i class="fas fa-times"></i> Premium signals</li>
            <li><i class="fas fa-times"></i> Telegram community</li>
            <li><i class="fas fa-times"></i> Advanced features</li>
        `;
    }
}

function loadRecentActivity() {
    const activityList = document.getElementById('activityList');
    
    // Mock activity data
    const activities = [
        {
            icon: 'fas fa-play',
            title: 'Started Forex Basics Course',
            time: '2 hours ago'
        },
        {
            icon: 'fas fa-trophy',
            title: 'Completed Technical Analysis Module',
            time: '1 day ago'
        },
        {
            icon: 'fas fa-chart-line',
            title: 'Received new trading signal',
            time: '2 days ago'
        }
    ];
    
    activityList.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon">
                <i class="${activity.icon}"></i>
            </div>
            <div class="activity-content">
                <h4>${activity.title}</h4>
                <p>${activity.time}</p>
            </div>
        </div>
    `).join('');
}

function loadCourses() {
    const coursesGrid = document.getElementById('coursesGrid');
    
    // Mock courses data
    const courses = [
        {
            title: 'Forex Basics',
            progress: 80,
            status: 'In Progress'
        },
        {
            title: 'Technical Analysis',
            progress: 100,
            status: 'Completed'
        },
        {
            title: 'Risk Management',
            progress: 30,
            status: 'In Progress'
        }
    ];
    
    coursesGrid.innerHTML = courses.map(course => `
        <div class="course-card">
            <h4>${course.title}</h4>
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${course.progress}%"></div>
            </div>
            <p>Progress: ${course.progress}%</p>
            <span class="status ${course.status.toLowerCase().replace(' ', '-')}">${course.status}</span>
        </div>
    `).join('');
}

function loadSubscription() {
    const subscriptionContent = document.getElementById('subscriptionContent');
    
    subscriptionContent.innerHTML = `
        <div class="subscription-info">
            <h4>Current Plan: Premium</h4>
            <p>Next billing: January 15, 2024</p>
            <p>Amount: KSH 5,000/month</p>
            <div class="subscription-actions">
                <button class="btn btn-primary">Upgrade Plan</button>
                <button class="btn btn-secondary">Cancel Subscription</button>
            </div>
        </div>
    `;
}

function loadProfile() {
    const profileContent = document.getElementById('profileContent');
    
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    
    profileContent.innerHTML = `
        <form class="profile-form">
            <div class="form-group">
                <label for="profileName">Full Name</label>
                <input type="text" id="profileName" value="${userInfo.name || ''}" required>
            </div>
            <div class="form-group">
                <label for="profileEmail">Email</label>
                <input type="email" id="profileEmail" value="${userInfo.email || ''}" required>
            </div>
            <div class="form-group">
                <label for="profilePhone">Phone</label>
                <input type="tel" id="profilePhone" value="${userInfo.phone || ''}">
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">Update Profile</button>
            </div>
        </form>
    `;
}

function loadSignals() {
    const signalsContent = document.getElementById('signalsContent');
    
    // Mock signals data
    const signals = [
        { pair: 'EUR/USD', action: 'BUY', price: '1.0850', time: '2 hours ago' },
        { pair: 'GBP/USD', action: 'SELL', price: '1.2650', time: '4 hours ago' },
        { pair: 'USD/JPY', action: 'BUY', price: '148.50', time: '6 hours ago' }
    ];
    
    signalsContent.innerHTML = `
        <div class="signals-list">
            ${signals.map(signal => `
                <div class="signal-item">
                    <div class="signal-pair">${signal.pair}</div>
                    <div class="signal-action ${signal.action.toLowerCase()}">${signal.action}</div>
                    <div class="signal-price">${signal.price}</div>
                    <div class="signal-time">${signal.time}</div>
                </div>
            `).join('')}
        </div>
    `;
}

function loadTelegram() {
    // Telegram content is static in HTML
}

function initializeSidebarToggle() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
    }
}

function initializeLogout() {
    const logoutBtn = document.getElementById('logoutBtn');

    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();

            if (confirm('Are you sure you want to logout?')) {
                // Clear user data
                localStorage.removeItem('userInfo');
                localStorage.removeItem('userToken');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('authToken');

                // Call logout API to destroy session
                fetch('../api/logout.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(() => {
                    // Redirect to home page
                    window.location.href = '../';
                }).catch(() => {
                    // Redirect anyway
                    window.location.href = '../';
                });
            }
        });
    }
}

// M-Pesa Payment Integration
let currentPlan = {};
let paymentTimer = null;

function showUpgradeModal() {
    document.getElementById('upgradeModal').style.display = 'flex';
    showModalStep('planSelectionStep');
}

function closeUpgradeModal() {
    document.getElementById('upgradeModal').style.display = 'none';
    if (paymentTimer) {
        clearInterval(paymentTimer);
        paymentTimer = null;
    }
}

function showModalStep(stepId) {
    // Hide all steps
    document.querySelectorAll('.modal-step').forEach(step => {
        step.classList.remove('active');
        step.style.display = 'none';
    });

    // Show selected step
    const selectedStep = document.getElementById(stepId);
    if (selectedStep) {
        selectedStep.style.display = 'block';
        selectedStep.classList.add('active');
    }
}

function selectPlan(planType, billingCycle, amount) {
    currentPlan = {
        planType: planType,
        billingCycle: billingCycle,
        amount: amount
    };

    // Update payment step content
    document.getElementById('selectedPlanTitle').textContent =
        planType.charAt(0).toUpperCase() + planType.slice(1) + ' Plan';
    document.getElementById('paymentAmount').textContent =
        new Intl.NumberFormat().format(amount);
    document.getElementById('paymentPeriod').textContent =
        '/' + billingCycle;

    // Show payment step
    showModalStep('paymentStep');
}

function goBackToPlans() {
    showModalStep('planSelectionStep');
}

// Initialize payment form
document.addEventListener('DOMContentLoaded', function() {
    const paymentForm = document.getElementById('paymentForm');
    if (paymentForm) {
        paymentForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            await processPayment();
        });
    }
});

async function processPayment() {
    const phoneNumber = document.getElementById('phoneNumber').value;
    const payBtn = document.getElementById('payBtn');

    // Validate phone number
    if (!phoneNumber || phoneNumber.length !== 9) {
        alert('Please enter a valid phone number (9 digits)');
        return;
    }

    // Format phone number
    const formattedPhone = '254' + phoneNumber;

    // Get user ID
    const userIdMeta = document.querySelector('meta[name="user-id"]');
    const userId = userIdMeta ? userIdMeta.content : null;

    if (!userId) {
        alert('User session error. Please refresh and try again.');
        return;
    }

    // Disable button and show loading
    payBtn.disabled = true;
    payBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

    try {
        const response = await fetch('../api/mpesa.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone: formattedPhone,
                amount: currentPlan.amount,
                plan_type: currentPlan.planType,
                billing_cycle: currentPlan.billingCycle,
                user_id: userId
            })
        });

        const data = await response.json();

        if (data.success) {
            // Show processing step
            showModalStep('processingStep');
            startPaymentTimer();

            // Check payment status
            checkPaymentStatus(data.checkout_request_id);
        } else {
            showError(data.message || 'Payment initiation failed');
        }

    } catch (error) {
        console.error('Payment error:', error);
        showError('Network error. Please try again.');
    } finally {
        // Reset button
        payBtn.disabled = false;
        payBtn.innerHTML = '<i class="fas fa-mobile-alt"></i> Pay with M-Pesa';
    }
}

function startPaymentTimer() {
    let timeLeft = 120; // 2 minutes
    const countdownEl = document.getElementById('countdown');

    paymentTimer = setInterval(() => {
        timeLeft--;
        if (countdownEl) {
            countdownEl.textContent = timeLeft;
        }

        if (timeLeft <= 0) {
            clearInterval(paymentTimer);
            showError('Payment timeout. Please try again.');
        }
    }, 1000);
}

async function checkPaymentStatus(checkoutRequestId) {
    let attempts = 0;
    const maxAttempts = 24; // Check for 2 minutes (5 second intervals)

    const statusCheck = setInterval(async () => {
        attempts++;

        try {
            const response = await fetch('../api/callback.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'check_status',
                    checkout_request_id: checkoutRequestId
                })
            });

            const data = await response.json();

            if (data.status === 'completed') {
                clearInterval(statusCheck);
                if (paymentTimer) clearInterval(paymentTimer);
                showSuccess();
            } else if (data.status === 'failed' || attempts >= maxAttempts) {
                clearInterval(statusCheck);
                if (paymentTimer) clearInterval(paymentTimer);
                showError(data.message || 'Payment verification failed');
            }

        } catch (error) {
            console.error('Status check error:', error);
        }
    }, 5000); // Check every 5 seconds
}

function showSuccess() {
    showModalStep('successStep');
}

function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    showModalStep('errorStep');
}

function retryPayment() {
    showModalStep('paymentStep');
}
