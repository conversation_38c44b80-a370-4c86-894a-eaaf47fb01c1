<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - ForexClass</title>
    <link rel="stylesheet" href="dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <span>ForexClass Admin</span>
                </div>
            </div>
            
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="welcome-text">Welcome back,</span>
                    <span class="user-name" id="userName">Admin User</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="#dashboard" class="nav-link active" data-page="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                            <span class="nav-badge">Overview</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#users" class="nav-link" data-page="users">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                            <span class="nav-badge">Manage Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#subscriptions" class="nav-link" data-page="subscriptions">
                            <i class="fas fa-crown"></i>
                            <span>Subscriptions</span>
                            <span class="nav-badge">User Plans</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#packages" class="nav-link" data-page="packages">
                            <i class="fas fa-box"></i>
                            <span>Packages</span>
                            <span class="nav-badge">Pricing Plans</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#packages" class="nav-link" data-page="packages">
                            <i class="fas fa-box"></i>
                            <span>Packages</span>
                            <span class="nav-badge">Pricing Plans</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#transactions" class="nav-link" data-page="transactions">
                            <i class="fas fa-credit-card"></i>
                            <span>Transactions</span>
                            <span class="nav-badge">Payments</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#telegram" class="nav-link" data-page="telegram">
                            <i class="fab fa-telegram"></i>
                            <span>Telegram</span>
                            <span class="nav-badge">Channel Management</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#messages" class="nav-link" data-page="messages">
                            <i class="fas fa-envelope"></i>
                            <span>Messages</span>
                            <span class="nav-badge">Notifications</span>
                        </a>
                    </li>
                </ul>
                
                <div class="sidebar-footer">
                    <a href="../index.html" class="nav-link">
                        <i class="fas fa-external-link-alt"></i>
                        <span>View Website</span>
                    </a>
                    <a href="#logout" class="nav-link logout" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="dashboard-header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="page-title">
                        <h1 id="pageTitle">Dashboard Overview</h1>
                        <p id="pageSubtitle">Monitor your platform performance</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="exchange-rate">
                        <span class="rate-label">Exchange Rate:</span>
                        <span class="rate-value">1 USD = 129.14 KSH</span>
                        <i class="fas fa-sync-alt rate-refresh" id="refreshRate"></i>
                    </div>
                    <div class="header-actions">
                        <button class="notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </button>
                        <div class="user-menu">
                            <div class="user-avatar-small">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <!-- Dashboard Overview Page -->
                <div class="page-content" id="dashboardPage">
                    <div class="content-header">
                        <div class="content-title">
                            <i class="fas fa-tachometer-alt"></i>
                            <h2>Dashboard Overview</h2>
                        </div>
                        <div class="content-actions">
                            <a href="/adduser" class="btn btn-success">
                                <i class="fas fa-user-plus"></i>
                                Add User
                            </a>
                            <a href="/addpackage" class="btn btn-success">
                                <i class="fas fa-box"></i>
                                Add Package
                            </a>
                            <button class="btn btn-primary" onclick="loadDashboardData()">
                                <i class="fas fa-refresh"></i>
                                Refresh Data
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-download"></i>
                                Export Report
                            </button>
                        </div>
                    </div>

                    <!-- Overview Stats -->
                    <div class="stats-grid">
                        <div class="stat-card" data-stat="users-stat">
                            <div class="stat-icon total">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="totalUsers">Loading...</h3>
                                <p>Total Users</p>
                                <span class="stat-change positive" id="newUsersChange">Loading...</span>
                            </div>
                        </div>
                        <div class="stat-card" data-stat="revenue-stat">
                            <div class="stat-icon completed">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="monthlyRevenue">Loading...</h3>
                                <p>Monthly Revenue</p>
                                <span class="stat-change positive" id="totalRevenueChange">Loading...</span>
                            </div>
                        </div>
                        <div class="stat-card" data-stat="subscriptions-stat">
                            <div class="stat-icon pending">
                                <i class="fas fa-crown"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="activeSubscriptions">Loading...</h3>
                                <p>Active Subscriptions</p>
                                <span class="stat-change neutral" id="subscriptionsChange">Loading...</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon failed">
                                <i class="fab fa-telegram"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value">12,450</h3>
                                <p>Telegram Members</p>
                                <span class="stat-change positive">****%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Dashboard Content Grid -->
                    <div class="dashboard-content-grid">
                        <!-- Subscription Breakdown -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Subscription Breakdown</h3>
                                <p>Active subscriptions by plan type</p>
                            </div>
                            <div class="card-content">
                                <div class="subscription-breakdown">
                                    <p>Loading subscription data...</p>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Transactions -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Recent Transactions</h3>
                                <p>Latest payment transactions</p>
                            </div>
                            <div class="card-content">
                                <div class="table-container">
                                    <table class="data-table" id="transactionsTable">
                                        <thead>
                                            <tr>
                                                <th>Transaction</th>
                                                <th>User</th>
                                                <th>Plan</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td colspan="6">Loading transactions...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions Grid -->
                    <div class="quick-actions-dashboard">
                        <div class="action-card-dash">
                            <div class="action-icon-dash">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <h4>Add New User</h4>
                            <p>Create a new user account</p>
                            <button class="btn btn-primary">Add User</button>
                        </div>
                        <div class="action-card-dash">
                            <div class="action-icon-dash">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <h4>Create Course</h4>
                            <p>Add a new trading course</p>
                            <button class="btn btn-primary">Create Course</button>
                        </div>
                        <div class="action-card-dash">
                            <div class="action-icon-dash">
                                <i class="fas fa-broadcast-tower"></i>
                            </div>
                            <h4>Send Signal</h4>
                            <p>Broadcast trading signal</p>
                            <button class="btn btn-primary">Send Signal</button>
                        </div>
                        <div class="action-card-dash">
                            <div class="action-icon-dash">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <h4>Send Message</h4>
                            <p>Send notification to users</p>
                            <button class="btn btn-primary">Send Message</button>
                        </div>
                    </div>
                </div>

                <!-- Subscriptions Management Page -->
                <div class="page-content" id="subscriptionsPage" style="display: none;">
                    <div class="content-header">
                        <div class="content-title">
                            <i class="fas fa-crown"></i>
                            <h2>Subscription Management</h2>
                        </div>
                        <div class="content-actions">
                            <button class="btn btn-secondary">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Add Subscription
                            </button>
                        </div>
                    </div>

                    <!-- Subscription Stats -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon completed">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3>1,247</h3>
                                <p>Active Subscribers</p>
                                <span class="stat-change positive">+18.2%</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon pending">
                                <i class="fas fa-crown"></i>
                            </div>
                            <div class="stat-content">
                                <h3>KSH 2.8M</h3>
                                <p>Monthly Revenue</p>
                                <span class="stat-change positive">+25.4%</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon failed">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <h3>89</h3>
                                <p>Expiring Soon</p>
                                <span class="stat-change negative">+12.1%</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon total">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-content">
                                <h3>92.5%</h3>
                                <p>Renewal Rate</p>
                                <span class="stat-change positive">+3.2%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Plan Distribution Chart -->
                    <div class="plan-distribution">
                        <div class="chart-container">
                            <h3>Plan Distribution</h3>
                            <div class="plan-stats">
                                <div class="plan-stat basic">
                                    <div class="plan-color"></div>
                                    <div class="plan-info">
                                        <span class="plan-name">Basic Plan</span>
                                        <span class="plan-count">687 users (55%)</span>
                                    </div>
                                </div>
                                <div class="plan-stat premium">
                                    <div class="plan-color"></div>
                                    <div class="plan-info">
                                        <span class="plan-name">Premium Plan</span>
                                        <span class="plan-count">423 users (34%)</span>
                                    </div>
                                </div>
                                <div class="plan-stat vip">
                                    <div class="plan-color"></div>
                                    <div class="plan-info">
                                        <span class="plan-name">VIP Plan</span>
                                        <span class="plan-count">137 users (11%)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Subscriptions Table -->
                    <div class="table-container">
                        <div class="table-header">
                            <div class="table-title">
                                <h3>User Subscriptions</h3>
                                <span class="table-count">Showing 5 of 1,247 subscriptions</span>
                            </div>
                            <div class="table-filters">
                                <select class="filter-select">
                                    <option>All Plans</option>
                                    <option>Basic</option>
                                    <option>Premium</option>
                                    <option>VIP</option>
                                </select>
                                <select class="filter-select">
                                    <option>All Status</option>
                                    <option>Active</option>
                                    <option>Expired</option>
                                    <option>Cancelled</option>
                                </select>
                                <input type="search" placeholder="Search users..." class="search-input">
                            </div>
                        </div>

                        <div class="table-wrapper">
                            <table class="subscriptions-table">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Plan</th>
                                        <th>Status</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Amount</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="subscriptionsTableBody">
                                    <tr>
                                        <td>
                                            <div class="user-info-cell">
                                                <span class="user-name">John Doe</span>
                                                <span class="user-email"><EMAIL></span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="plan-badge premium">Premium</span>
                                        </td>
                                        <td>
                                            <span class="status-badge active">Active</span>
                                        </td>
                                        <td>
                                            <span class="date-text">2024-01-15</span>
                                        </td>
                                        <td>
                                            <span class="date-text">2024-02-15</span>
                                        </td>
                                        <td>
                                            <div class="amount-info">
                                                <span class="amount-ksh">KSH 5,000</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-action extend">Extend</button>
                                                <button class="btn-action cancel">Cancel</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="user-info-cell">
                                                <span class="user-name">Jane Smith</span>
                                                <span class="user-email"><EMAIL></span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="plan-badge vip">VIP</span>
                                        </td>
                                        <td>
                                            <span class="status-badge active">Active</span>
                                        </td>
                                        <td>
                                            <span class="date-text">2024-01-10</span>
                                        </td>
                                        <td>
                                            <span class="date-text">2024-02-10</span>
                                        </td>
                                        <td>
                                            <div class="amount-info">
                                                <span class="amount-ksh">KSH 10,000</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-action extend">Extend</button>
                                                <button class="btn-action cancel">Cancel</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="user-info-cell">
                                                <span class="user-name">Mike Johnson</span>
                                                <span class="user-email"><EMAIL></span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="plan-badge basic">Basic</span>
                                        </td>
                                        <td>
                                            <span class="status-badge expired">Expired</span>
                                        </td>
                                        <td>
                                            <span class="date-text">2023-12-15</span>
                                        </td>
                                        <td>
                                            <span class="date-text">2024-01-15</span>
                                        </td>
                                        <td>
                                            <div class="amount-info">
                                                <span class="amount-ksh">KSH 2,500</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-action renew">Renew</button>
                                                <button class="btn-action upgrade">Upgrade</button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="table-pagination">
                            <div class="pagination-info">
                                Showing 1 to 3 of 1,247 entries
                            </div>
                            <div class="pagination-controls">
                                <button class="pagination-btn" disabled>Previous</button>
                                <button class="pagination-btn active">1</button>
                                <button class="pagination-btn">2</button>
                                <button class="pagination-btn">3</button>
                                <button class="pagination-btn">Next</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Package Management Page -->
                <div class="page-content" id="packagesPage" style="display: none;">
                    <div class="content-header">
                        <div class="content-title">
                            <i class="fas fa-box"></i>
                            <h2>Package Management</h2>
                        </div>
                        <div class="content-actions">
                            <button class="btn btn-primary" onclick="showCreatePackageModal()">
                                <i class="fas fa-plus"></i>
                                Create Package
                            </button>
                            <button class="btn btn-secondary" onclick="loadPackages()">
                                <i class="fas fa-refresh"></i>
                                Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Packages Table -->
                    <div class="content-section">
                        <div class="section-header">
                            <h3>Subscription Packages</h3>
                            <p>Manage pricing plans and features</p>
                        </div>
                        <div class="table-container">
                            <table class="data-table" id="packagesTable">
                                <thead>
                                    <tr>
                                        <th>Package</th>
                                        <th>Description</th>
                                        <th>Monthly Price</th>
                                        <th>Yearly Price</th>
                                        <th>Subscribers</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="7">Loading packages...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Transaction Management Page -->
                <div class="page-content" id="transactionsPage" style="display: none;">
                    <div class="content-header">
                        <div class="content-title">
                            <i class="fas fa-credit-card"></i>
                            <h2>Transaction Management</h2>
                        </div>
                        <div class="content-actions">
                            <button class="btn btn-secondary">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Add Transaction
                            </button>
                        </div>
                    </div>
                    
                    <!-- Transaction Stats -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon completed">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3>KSH 145,850</h3>
                                <p>Completed Transactions</p>
                                <span class="stat-change positive">+12.5%</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon pending">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <h3>KSH 34,350</h3>
                                <p>Pending Transactions</p>
                                <span class="stat-change neutral">+2.1%</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon failed">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3>KSH 29,850</h3>
                                <p>Failed Transactions</p>
                                <span class="stat-change negative">-5.2%</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon total">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-content">
                                <h3>KSH 210,050</h3>
                                <p>Total Volume</p>
                                <span class="stat-change positive">****%</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Transaction Table -->
                    <div class="table-container">
                        <div class="table-header">
                            <div class="table-title">
                                <h3>Recent Transactions</h3>
                                <span class="table-count">Showing 3 of 156 transactions</span>
                            </div>
                            <div class="table-filters">
                                <select class="filter-select">
                                    <option>All Status</option>
                                    <option>Completed</option>
                                    <option>Pending</option>
                                    <option>Failed</option>
                                </select>
                                <input type="search" placeholder="Search transactions..." class="search-input">
                            </div>
                        </div>
                        
                        <div class="table-wrapper">
                            <table class="transactions-table">
                                <thead>
                                    <tr>
                                        <th>Transaction</th>
                                        <th>User</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="transactionsTableBody">
                                    <tr>
                                        <td>
                                            <div class="transaction-info">
                                                <span class="transaction-id">TXN001</span>
                                                <span class="transaction-date">2024-01-15 14:30</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="user-info-cell">
                                                <span class="user-name">John Doe</span>
                                                <span class="user-email"><EMAIL></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="amount-info">
                                                <span class="amount-ksh">KSH 14,850</span>
                                                <span class="amount-usd">$115</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="status-badge completed">completed</span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-action approve">Approve</button>
                                                <button class="btn-action reject">Reject</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="transaction-info">
                                                <span class="transaction-id">TXN002</span>
                                                <span class="transaction-date">2024-01-16 09:15</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="user-info-cell">
                                                <span class="user-name">Jane Smith</span>
                                                <span class="user-email"><EMAIL></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="amount-info">
                                                <span class="amount-ksh">KSH 4,350</span>
                                                <span class="amount-usd">$34</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="status-badge pending">pending</span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-action approve">Approve</button>
                                                <button class="btn-action reject">Reject</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="transaction-info">
                                                <span class="transaction-id">TXN003</span>
                                                <span class="transaction-date">2024-01-16 16:45</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="user-info-cell">
                                                <span class="user-name">Mike Johnson</span>
                                                <span class="user-email"><EMAIL></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="amount-info">
                                                <span class="amount-ksh">KSH 29,850</span>
                                                <span class="amount-usd">$231</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="status-badge failed">failed</span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-action approve">Approve</button>
                                                <button class="btn-action reject">Reject</button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="table-pagination">
                            <div class="pagination-info">
                                Showing 1 to 3 of 156 entries
                            </div>
                            <div class="pagination-controls">
                                <button class="pagination-btn" disabled>Previous</button>
                                <button class="pagination-btn active">1</button>
                                <button class="pagination-btn">2</button>
                                <button class="pagination-btn">3</button>
                                <button class="pagination-btn">Next</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Subscription Modal -->
    <div id="subscriptionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="subscriptionModalTitle">Add New Subscription</h3>
                <span class="close" id="closeSubscriptionModal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="subscriptionForm">
                    <div class="form-group">
                        <label for="userSelect">Select User</label>
                        <select id="userSelect" required>
                            <option value="">Choose a user...</option>
                            <option value="1">John Doe (<EMAIL>)</option>
                            <option value="2">Jane Smith (<EMAIL>)</option>
                            <option value="3">Mike Johnson (<EMAIL>)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="planSelect">Subscription Plan</label>
                        <select id="planSelect" required>
                            <option value="">Choose a plan...</option>
                            <option value="basic" data-price="2500">Basic Plan - KSH 2,500/month</option>
                            <option value="premium" data-price="5000">Premium Plan - KSH 5,000/month</option>
                            <option value="vip" data-price="10000">VIP Plan - KSH 10,000/month</option>
                        </select>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="startDate">Start Date</label>
                            <input type="date" id="startDate" required>
                        </div>
                        <div class="form-group">
                            <label for="duration">Duration</label>
                            <select id="duration" required>
                                <option value="1">1 Month</option>
                                <option value="3">3 Months</option>
                                <option value="6">6 Months</option>
                                <option value="12">12 Months</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="paymentMethod">Payment Method</label>
                        <select id="paymentMethod" required>
                            <option value="mpesa">M-Pesa</option>
                            <option value="bank">Bank Transfer</option>
                            <option value="card">Credit Card</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="amount">Amount (KSH)</label>
                        <input type="number" id="amount" readonly>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="autoRenew" checked>
                            Enable auto-renewal
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="cancelSubscription">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Subscription</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Package Creation/Edit Modal -->
    <div class="modal-overlay" id="packageModal" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 id="packageModalTitle">Create Package</h3>
                <button class="modal-close" onclick="closePackageModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="packageForm">
                    <input type="hidden" id="packageId">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="packageName">Package Name</label>
                            <input type="text" id="packageName" placeholder="e.g., Premium Plan" required>
                        </div>
                        <div class="form-group">
                            <label for="packageType">Plan Type</label>
                            <input type="text" id="packageType" placeholder="e.g., premium" required>
                            <small>Unique identifier (lowercase, no spaces)</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="packageDescription">Description</label>
                        <textarea id="packageDescription" rows="3" placeholder="Describe what this package includes..." required></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="monthlyPrice">Monthly Price (KSH)</label>
                            <input type="number" id="monthlyPrice" placeholder="2500" min="0" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="yearlyPrice">Yearly Price (KSH)</label>
                            <input type="number" id="yearlyPrice" placeholder="25000" min="0" step="0.01" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="packageFeatures">Features</label>
                        <textarea id="packageFeatures" rows="5" placeholder="Enter features, one per line:
Access to basic courses
Email support
Trading signals
Community access" required></textarea>
                        <small>Enter each feature on a new line</small>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="courseAccessLevel">Course Access Level</label>
                            <select id="courseAccessLevel">
                                <option value="1">Level 1 - Basic</option>
                                <option value="2">Level 2 - Intermediate</option>
                                <option value="3">Level 3 - Advanced</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="maxDevices">Max Devices</label>
                            <input type="number" id="maxDevices" value="2" min="1" max="10">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="supportLevel">Support Level</label>
                            <select id="supportLevel">
                                <option value="basic">Basic Support</option>
                                <option value="priority">Priority Support</option>
                                <option value="premium">Premium Support</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="telegramAccess">
                                Telegram Access
                            </label>
                            <label>
                                <input type="checkbox" id="signalAccess" checked>
                                Trading Signals
                            </label>
                            <label>
                                <input type="checkbox" id="isActive" checked>
                                Active Package
                            </label>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closePackageModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="packageSubmitBtn">Create Package</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
