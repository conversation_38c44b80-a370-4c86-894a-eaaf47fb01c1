/* Modern Hero Section */
.hero {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 120px 0 80px;
}

/* Background Elements */
.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
    animation: patternMove 20s ease-in-out infinite;
}

@keyframes patternMove {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-10px) translateY(-10px); }
    50% { transform: translateX(10px) translateY(-5px); }
    75% { transform: translateX(-5px) translateY(10px); }
}

/* Floating Shapes */
.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
    animation: float 15s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    left: 80%;
    animation-delay: 3s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 6s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 20%;
    animation-delay: 9s;
}

.shape-5 {
    width: 40px;
    height: 40px;
    top: 40%;
    right: 10%;
    animation-delay: 12s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-20px) rotate(90deg); }
    50% { transform: translateY(10px) rotate(180deg); }
    75% { transform: translateY(-10px) rotate(270deg); }
}

/* Hero Container */
.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 1;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
    min-height: 80vh;
}

/* Hero Left Content */
.hero-left {
    color: #fff;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(79, 70, 229, 0.2);
    border: 1px solid rgba(79, 70, 229, 0.3);
    border-radius: 50px;
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 600;
    color: #a78bfa;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
}

.hero-badge i {
    color: #fbbf24;
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: #fff;
}

.gradient-text {
    background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 50%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 3rem;
    max-width: 500px;
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: 3rem;
    margin-bottom: 3rem;
}

.stat-item {
    text-align: left;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: #fff;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Hero CTA Buttons */
.hero-cta {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
}

.btn-primary-hero {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: #fff;
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3);
}

.btn-primary-hero:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(79, 70, 229, 0.4);
}

.btn-secondary-hero {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.2);
    padding: 14px 30px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary-hero:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
}

/* Trust Indicators */
.trust-indicators {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
}

.trust-item i {
    color: #22c55e;
    font-size: 16px;
}

/* Hero Right - Dashboard Showcase */
.hero-right {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dashboard-showcase {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.dashboard-main {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 24px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.dashboard-main:hover {
    transform: translateY(-5px);
    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.3);
}

/* Dashboard Header */
.dashboard-header {
    margin-bottom: 24px;
}

.header-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.user-welcome {
    display: flex;
    align-items: center;
    gap: 12px;
}

.avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(79, 70, 229, 0.5);
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.welcome-text {
    display: flex;
    flex-direction: column;
}

.greeting {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.username {
    font-size: 18px;
    color: #fff;
    font-weight: 700;
    margin-top: 2px;
}

.notification-bell {
    position: relative;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-bell:hover {
    background: rgba(255, 255, 255, 0.2);
}

.notification-bell i {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

.notification-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: #ef4444;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* Portfolio Overview */
.portfolio-overview {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
    border: 1px solid rgba(79, 70, 229, 0.2);
    border-radius: 20px;
    padding: 24px;
    margin-bottom: 20px;
}

.portfolio-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.portfolio-header h3 {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.time-filter {
    display: flex;
    gap: 8px;
}

.time-filter span {
    padding: 4px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.6);
}

.time-filter span.active {
    background: rgba(79, 70, 229, 0.3);
    color: #a78bfa;
}

.portfolio-value {
    display: flex;
    align-items: baseline;
    margin-bottom: 8px;
}

.currency {
    font-size: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
}

.amount {
    font-size: 32px;
    color: #fff;
    font-weight: 900;
    margin: 0 4px;
}

.cents {
    font-size: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
}

.portfolio-change {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 16px;
}

.portfolio-change i {
    color: #22c55e;
    font-size: 14px;
}

.change-amount {
    color: #22c55e;
    font-weight: 700;
    font-size: 14px;
}

.change-percent {
    color: #22c55e;
    font-weight: 600;
    font-size: 14px;
}

.mini-chart {
    height: 60px;
    width: 100%;
}

.chart-svg {
    width: 100%;
    height: 100%;
}

/* Quick Actions Grid */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    margin-bottom: 20px;
}

.action-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #fff;
}

.action-icon.buy {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.action-icon.sell {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.action-icon.swap {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.action-icon.more {
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

.action-card span {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    font-weight: 500;
}

/* Live Signals */
.live-signals {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 20px;
}

.signals-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.signals-header h4 {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: 20px;
    padding: 4px 12px;
    font-size: 11px;
    font-weight: 600;
    color: #22c55e;
    text-transform: uppercase;
}

.pulse-dot {
    width: 6px;
    height: 6px;
    background: #22c55e;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.signals-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.signal-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.signal-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.pair {
    color: #fff;
    font-weight: 600;
    font-size: 14px;
}

.signal-type {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
}

.signal-type.buy {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.signal-type.sell {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.signal-profit {
    font-weight: 700;
    font-size: 14px;
}

.signal-profit.positive {
    color: #22c55e;
}

/* Performance Cards */
.performance-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transition: all 0.4s ease;
    z-index: 1;
}

.performance-card:hover {
    transform: translateY(-5px) scale(1.05);
    background: rgba(255, 255, 255, 0.15);
}

.card-1 {
    top: 10%;
    right: -15%;
    animation: floatCard1 8s ease-in-out infinite;
}

.card-2 {
    top: 45%;
    right: -20%;
    animation: floatCard2 8s ease-in-out infinite 2s;
}

.card-3 {
    bottom: 15%;
    right: -15%;
    animation: floatCard3 8s ease-in-out infinite 4s;
}

@keyframes floatCard1 {
    0%, 100% { transform: translateY(0px) translateX(0px); }
    25% { transform: translateY(-10px) translateX(5px); }
    50% { transform: translateY(5px) translateX(-3px); }
    75% { transform: translateY(-5px) translateX(8px); }
}

@keyframes floatCard2 {
    0%, 100% { transform: translateY(0px) translateX(0px); }
    25% { transform: translateY(8px) translateX(-5px); }
    50% { transform: translateY(-8px) translateX(5px); }
    75% { transform: translateY(3px) translateX(-8px); }
}

@keyframes floatCard3 {
    0%, 100% { transform: translateY(0px) translateX(0px); }
    25% { transform: translateY(-5px) translateX(8px); }
    50% { transform: translateY(10px) translateX(-3px); }
    75% { transform: translateY(-8px) translateX(5px); }
}

.perf-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #fff;
}

.card-1 .perf-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.card-2 .perf-icon {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.card-3 .perf-icon {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.perf-content {
    display: flex;
    flex-direction: column;
}

.perf-value {
    color: #fff;
    font-size: 18px;
    font-weight: 800;
    line-height: 1;
}

.perf-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    font-weight: 500;
    margin-top: 2px;
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
    border-radius: 34px;
    overflow: hidden;
    position: relative;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px 8px;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
}

.time {
    font-weight: 700;
    letter-spacing: 0.5px;
}

.status-icons {
    display: flex;
    gap: 6px;
    font-size: 14px;
    opacity: 0.8;
}

.app-content {
    padding: 0 24px 24px;
    height: calc(100% - 60px);
    overflow-y: auto;
}

.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    margin-bottom: 24px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.greeting {
    color: #94a3b8;
    font-size: 14px;
    font-weight: 500;
}

.username {
    color: #fff;
    font-size: 18px;
    font-weight: 700;
    margin-top: 2px;
}

.notification-icon {
    color: #64748b;
    font-size: 20px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.notification-icon:hover {
    color: #4f46e5;
}

/* Balance Card */
.balance-card {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.2), rgba(124, 58, 237, 0.2));
    border: 1px solid rgba(79, 70, 229, 0.3);
    border-radius: 20px;
    padding: 24px;
    margin-bottom: 24px;
    backdrop-filter: blur(20px);
}

.balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.balance-label {
    color: #94a3b8;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.balance-toggle {
    color: #64748b;
    font-size: 16px;
    cursor: pointer;
}

.balance-amount {
    color: #fff;
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
}

.balance-change {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #22c55e;
    font-size: 14px;
    font-weight: 600;
}

.balance-change i {
    font-size: 12px;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 32px;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.action-item:hover {
    transform: translateY(-2px);
}

.action-icon {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #fff;
    transition: all 0.3s ease;
}

.action-icon.deposit {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.action-icon.withdraw {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.action-icon.trade {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.action-icon.signals {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.action-item span {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

/* Live Signals Section */
.live-signals-section {
    margin-bottom: 24px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-title {
    color: #fff;
    font-size: 18px;
    font-weight: 700;
}

.live-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: 20px;
    padding: 6px 12px;
    color: #22c55e;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.live-dot {
    width: 6px;
    height: 6px;
    background: #22c55e;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Signal Cards */
.signals-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.signal-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.signal-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
}

.signal-pair {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.pair-name {
    color: #fff;
    font-size: 16px;
    font-weight: 700;
}

.signal-type {
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: fit-content;
}

.buy-signal .signal-type {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.sell-signal .signal-type {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.signal-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.entry-price {
    color: #94a3b8;
    font-size: 14px;
    font-weight: 500;
}

.profit {
    color: #22c55e;
    font-size: 14px;
    font-weight: 700;
}

/* Floating Cards */
.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transition: all 0.4s ease;
    animation: floatCard 8s ease-in-out infinite;
}

.floating-card:hover {
    transform: translateY(-5px) scale(1.05);
    background: rgba(255, 255, 255, 0.15);
}

.stats-card {
    top: 10%;
    right: -20%;
    animation-delay: 0s;
}

.profit-card {
    top: 50%;
    right: -25%;
    animation-delay: 2s;
}

.course-card {
    bottom: 15%;
    right: -20%;
    animation-delay: 4s;
}

@keyframes floatCard {
    0%, 100% { transform: translateY(0px) translateX(0px); }
    25% { transform: translateY(-10px) translateX(5px); }
    50% { transform: translateY(5px) translateX(-3px); }
    75% { transform: translateY(-5px) translateX(8px); }
}

.card-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #fff;
}

.stats-card .card-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.profit-card .card-icon {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.course-card .card-icon {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.card-content {
    display: flex;
    flex-direction: column;
}

.card-value {
    color: #fff;
    font-size: 18px;
    font-weight: 800;
    line-height: 1;
}

.card-label {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 500;
    margin-top: 2px;
}

.signals-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
}

.live-indicator {
    color: #22c55e;
    font-size: 12px;
    font-weight: 500;
}

.signal-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.signal-item:last-child {
    border-bottom: none;
}

.signal-pair {
    color: #fff;
    font-size: 13px;
    font-weight: 600;
}

.signal-action {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
}

.signal-action.buy {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.signal-action.sell {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.signal-profit {
    color: #22c55e;
    font-size: 12px;
    font-weight: 600;
}

.course-progress {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 16px;
}

.progress-title {
    color: #94a3b8;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    margin-bottom: 4px;
}

.progress-course {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    height: 6px;
    margin-bottom: 8px;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
    height: 100%;
    border-radius: 8px;
    transition: width 0.3s ease;
}

.progress-text {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 3rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    animation: bounce 3s ease-in-out infinite;
}

.scroll-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.scroll-arrow:hover {
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.1);
}

.scroll-arrow i {
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
}

@keyframes bounce {
    0%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    50% {
        transform: translateX(-50%) translateY(-10px);
    }
}

/* Enhanced Navbar Responsiveness */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    color: #4f46e5;
    font-weight: 800;
    font-size: 1.5rem;
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    font-size: 15px;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: #4f46e5;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #4f46e5;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-buttons {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.nav-buttons .btn-primary {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    color: #fff;
    text-decoration: none;
    font-size: 14px;
}

.nav-buttons .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

.nav-buttons .btn-secondary {
    background: transparent;
    color: #4f46e5;
    border: 2px solid #4f46e5;
    padding: 8px 18px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 14px;
}

.nav-buttons .btn-secondary:hover {
    background: #4f46e5;
    color: #fff;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-container {
        padding: 0 20px;
    }

    .hero-content {
        gap: 4rem;
    }

    .performance-card {
        display: none;
    }
}

@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 4rem;
        text-align: center;
    }

    .hero-left {
        order: 1;
    }

    .hero-right {
        order: 2;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .hero-stats {
        justify-content: center;
    }

    .hero-cta {
        justify-content: center;
    }

    .trust-indicators {
        justify-content: center;
    }

    .dashboard-main {
        max-width: 400px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .hero {
        padding: 100px 0 60px;
    }

    .hero-container {
        padding: 0 20px;
    }

    .hero-content {
        gap: 3rem;
    }

    .hero-title {
        font-size: 2.8rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .hero-stats {
        gap: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .hero-cta {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-primary-hero,
    .btn-secondary-hero {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .trust-indicators {
        flex-direction: column;
        gap: 1rem;
    }

    .dashboard-main {
        padding: 20px;
    }

    .portfolio-value .amount {
        font-size: 28px;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 80px 0 40px;
    }

    .hero-container {
        padding: 0 16px;
    }

    .hero-content {
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-stats {
        gap: 1.5rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .hero-badge {
        padding: 6px 16px;
        font-size: 12px;
    }

    .dashboard-main {
        padding: 16px;
    }

    .portfolio-overview {
        padding: 20px;
    }

    .portfolio-value .amount {
        font-size: 24px;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
    }

    .action-card {
        padding: 12px;
    }

    .action-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .action-card span {
        font-size: 10px;
    }

    .scroll-indicator {
        bottom: 2rem;
    }

    .scroll-text {
        font-size: 12px;
    }
}

/* Complete Website Responsiveness */

/* Navbar Mobile Responsiveness */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 16px;
        height: 60px;
    }

    .nav-logo h2 {
        font-size: 1.3rem;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 60px;
        flex-direction: column;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
        gap: 1.5rem;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 0;
    }

    .nav-menu a {
        font-size: 16px;
        padding: 10px 0;
        display: block;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:first-child {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .nav-buttons {
        gap: 0.5rem;
    }

    .nav-buttons .btn-primary,
    .nav-buttons .btn-secondary {
        padding: 8px 16px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 12px;
    }

    .nav-logo h2 {
        font-size: 1.2rem;
    }

    .nav-buttons {
        flex-direction: column;
        gap: 0.3rem;
    }

    .nav-buttons .btn-primary,
    .nav-buttons .btn-secondary {
        padding: 6px 12px;
        font-size: 11px;
        min-width: 70px;
    }
}

/* Section Responsiveness */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
    padding: 0 20px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .section-header {
        margin-bottom: 3rem;
        padding: 0 16px;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .section-header p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .section-header {
        margin-bottom: 2rem;
        padding: 0 12px;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .section-header p {
        font-size: 0.95rem;
    }
}

/* Container Responsiveness */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

@media (max-width: 1200px) {
    .container {
        padding: 0 20px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 12px;
    }
}
