<?php
/**
 * Student Profile Management
 * 
 * Handles student profile viewing and editing
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

session_start();
require_once '../api/config.php';

// Check if student is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'user') {
    header('Location: index.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$errors = [];
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Validation
    if (empty($name)) {
        $errors[] = "Name is required";
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Valid email is required";
    }
    
    // If changing password
    if (!empty($new_password)) {
        if (empty($current_password)) {
            $errors[] = "Current password is required to change password";
        }
        
        if (strlen($new_password) < 6) {
            $errors[] = "New password must be at least 6 characters";
        }
        
        if ($new_password !== $confirm_password) {
            $errors[] = "New passwords do not match";
        }
    }
    
    if (empty($errors)) {
        try {
            $pdo = getDBConnection();
            
            // Check if email already exists for another user
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $user_id]);
            
            if ($stmt->fetch()) {
                $errors[] = "Email already exists";
            } else {
                // If changing password, verify current password
                if (!empty($new_password)) {
                    $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
                    $stmt->execute([$user_id]);
                    $user = $stmt->fetch();
                    
                    if (!password_verify($current_password, $user['password'])) {
                        $errors[] = "Current password is incorrect";
                    } else {
                        // Update with new password
                        $hashedPassword = password_hash($new_password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("
                            UPDATE users 
                            SET name = ?, email = ?, phone = ?, password = ?, updated_at = NOW() 
                            WHERE id = ?
                        ");
                        $stmt->execute([$name, $email, $phone, $hashedPassword, $user_id]);
                    }
                } else {
                    // Update without password change
                    $stmt = $pdo->prepare("
                        UPDATE users 
                        SET name = ?, email = ?, phone = ?, updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $stmt->execute([$name, $email, $phone, $user_id]);
                }
                
                if (empty($errors)) {
                    // Update session variables
                    $_SESSION['user_name'] = $name;
                    $_SESSION['user_email'] = $email;
                    
                    $success = "Profile updated successfully!";
                }
            }
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

// Get current user data
try {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        header('Location: index.php');
        exit();
    }
} catch (Exception $e) {
    $errors[] = "Failed to load user data";
}

$page_title = "Profile Settings - ForexClass Student Portal";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="user-id" content="<?php echo $user_id; ?>">
    <meta name="user-name" content="<?php echo htmlspecialchars($user['name'] ?? 'Student'); ?>">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <h2>ForexClass</h2>
                    <span>Student Portal</span>
                </div>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="courses.php" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            <span>My Courses</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="subscription.php" class="nav-link">
                            <i class="fas fa-crown"></i>
                            <span>Subscription</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="profile.php" class="nav-link">
                            <i class="fas fa-user"></i>
                            <span>Profile</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="signals.php" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span>Trading Signals</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="telegram.php" class="nav-link">
                            <i class="fab fa-telegram"></i>
                            <span>Telegram</span>
                        </a>
                    </li>
                </ul>

                <div class="sidebar-footer">
                    <a href="../" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>Back to Website</span>
                    </a>
                    <a href="#logout" class="nav-link logout" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="dashboard-header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="page-title">
                        <h1><i class="fas fa-user-edit"></i> Profile Settings</h1>
                        <p>Manage your account information and preferences</p>
                    </div>
                </div>

                <div class="header-right">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <span class="user-name"><?php echo htmlspecialchars($user['name'] ?? 'Student'); ?></span>
                            <span class="user-role">Student</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Profile Content -->
            <div class="dashboard-content">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <div class="profile-tabs">
                <div class="tab-nav">
                    <button class="tab-btn active" data-tab="personal">
                        <i class="fas fa-user"></i>
                        Personal Information
                    </button>
                    <button class="tab-btn" data-tab="security">
                        <i class="fas fa-shield-alt"></i>
                        Security
                    </button>
                    <button class="tab-btn" data-tab="payments">
                        <i class="fas fa-credit-card"></i>
                        Payment History
                    </button>
                    <button class="tab-btn" data-tab="preferences">
                        <i class="fas fa-cog"></i>
                        Preferences
                    </button>
                </div>
                
                <div class="tab-content">
                    <!-- Personal Information Tab -->
                    <div class="tab-pane active" id="personal">
                        <form method="POST" class="profile-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="name">Full Name</label>
                                    <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($user['name'] ?? ''); ?>" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label>Member Since</label>
                                    <input type="text" value="<?php echo date('F j, Y', strtotime($user['created_at'])); ?>" readonly>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Profile
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Security Tab -->
                    <div class="tab-pane" id="security">
                        <form method="POST" class="profile-form">
                            <input type="hidden" name="name" value="<?php echo htmlspecialchars($user['name']); ?>">
                            <input type="hidden" name="email" value="<?php echo htmlspecialchars($user['email']); ?>">
                            <input type="hidden" name="phone" value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                            
                            <div class="form-group">
                                <label for="current_password">Current Password</label>
                                <input type="password" id="current_password" name="current_password" placeholder="Enter current password">
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="new_password">New Password</label>
                                    <input type="password" id="new_password" name="new_password" placeholder="Enter new password">
                                    <small>Minimum 6 characters</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="confirm_password">Confirm New Password</label>
                                    <input type="password" id="confirm_password" name="confirm_password" placeholder="Confirm new password">
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-key"></i> Change Password
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Payment History Tab -->
                    <div class="tab-pane" id="payments">
                        <div class="payments-content">
                            <h4>Payment History</h4>
                            <div class="payment-summary">
                                <div class="summary-card">
                                    <h5>Total Spent</h5>
                                    <p class="amount" id="totalSpent">KSH 0</p>
                                </div>
                                <div class="summary-card">
                                    <h5>Active Subscription</h5>
                                    <p id="activeSubscription">None</p>
                                </div>
                                <div class="summary-card">
                                    <h5>Next Payment</h5>
                                    <p id="nextPayment">N/A</p>
                                </div>
                            </div>

                            <div class="payment-history">
                                <h5>Recent Transactions</h5>
                                <div class="transactions-table" id="transactionsTable">
                                    <div class="loading">Loading payment history...</div>
                                </div>
                            </div>

                            <div class="payment-actions">
                                <a href="subscription.php" class="btn btn-primary">
                                    <i class="fas fa-crown"></i>
                                    Manage Subscription
                                </a>
                                <button class="btn btn-secondary" onclick="downloadPaymentHistory()">
                                    <i class="fas fa-download"></i>
                                    Download History
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Preferences Tab -->
                    <div class="tab-pane" id="preferences">
                        <div class="preferences-content">
                            <h4>Notification Preferences</h4>
                            <div class="preference-item">
                                <label>
                                    <input type="checkbox" checked>
                                    Email notifications for new courses
                                </label>
                            </div>
                            <div class="preference-item">
                                <label>
                                    <input type="checkbox" checked>
                                    Trading signal alerts
                                </label>
                            </div>
                            <div class="preference-item">
                                <label>
                                    <input type="checkbox">
                                    Marketing emails
                                </label>
                            </div>
                            
                            <h4>Display Preferences</h4>
                            <div class="preference-item">
                                <label for="timezone">Timezone</label>
                                <select id="timezone">
                                    <option value="Africa/Nairobi">East Africa Time (EAT)</option>
                                    <option value="UTC">UTC</option>
                                </select>
                            </div>
                            
                            <div class="form-actions">
                                <button type="button" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Preferences
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            </div>
        </main>
    </div>

    <style>
        /* Profile-specific styles that extend dashboard.css */
        .profile-tabs {
            background: #fff;
            border-radius: 1rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        .tab-nav {
            display: flex;
            border-bottom: 1px solid #e2e8f0;
            background: #f8fafc;
        }
        .tab-pane {
            display: none;
            padding: 2rem;
        }
        .tab-pane.active {
            display: block;
        }
        .profile-form {
            max-width: 600px;
        }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 600; }
        .form-group input, .form-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .form-group small { color: #666; font-size: 12px; }
        .form-actions { margin-top: 30px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; text-decoration: none; display: inline-block; margin-right: 10px; cursor: pointer; }
        .btn-primary { background: #4f46e5; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .alert { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert ul { margin: 0; padding-left: 20px; }
        .preferences-content h4 { margin: 20px 0 10px 0; color: #1e293b; }
        .preference-item { margin-bottom: 15px; }
        .preference-item label { display: flex; align-items: center; gap: 10px; cursor: pointer; }
        .preference-item input[type="checkbox"] { width: auto; }

        /* Payment History Styles */
        .payments-content h4, .payments-content h5 { margin: 20px 0 10px 0; color: #1e293b; }
        .payment-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8fafc; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid #e2e8f0; }
        .summary-card h5 { margin: 0 0 10px 0; color: #64748b; font-size: 14px; }
        .summary-card p { margin: 0; font-size: 18px; font-weight: 600; color: #1e293b; }
        .summary-card .amount { color: #10b981; font-size: 24px; }
        .transactions-table { background: #fff; border: 1px solid #e2e8f0; border-radius: 10px; overflow: hidden; }
        .transaction-item { display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 20px; padding: 15px 20px; border-bottom: 1px solid #f1f5f9; align-items: center; }
        .transaction-item:last-child { border-bottom: none; }
        .transaction-header { background: #f8fafc; font-weight: 600; color: #374151; }
        .transaction-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .transaction-status.completed { background: #d1fae5; color: #065f46; }
        .transaction-status.pending { background: #fef3c7; color: #92400e; }
        .transaction-status.failed { background: #fee2e2; color: #991b1b; }
        .loading { padding: 40px; text-align: center; color: #64748b; }
        .payment-actions { margin-top: 30px; display: flex; gap: 15px; }
    </style>
    
    <script>
        // Tab functionality
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const tabId = this.dataset.tab;

                // Remove active class from all tabs and panes
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));

                // Add active class to clicked tab and corresponding pane
                this.classList.add('active');
                document.getElementById(tabId).classList.add('active');

                // Load payment history when payments tab is clicked
                if (tabId === 'payments') {
                    loadPaymentHistory();
                }
            });
        });

        // Load payment history
        async function loadPaymentHistory() {
            const tableContainer = document.getElementById('transactionsTable');

            try {
                const response = await fetch('../api/profile.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_payment_history',
                        user_id: <?php echo $user_id; ?>
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Update summary
                    document.getElementById('totalSpent').textContent = 'KSH ' + new Intl.NumberFormat().format(data.summary.total_spent);
                    document.getElementById('activeSubscription').textContent = data.summary.active_subscription || 'None';
                    document.getElementById('nextPayment').textContent = data.summary.next_payment || 'N/A';

                    // Update transactions table
                    if (data.transactions.length > 0) {
                        let tableHTML = `
                            <div class="transaction-item transaction-header">
                                <div>Date</div>
                                <div>Plan</div>
                                <div>Amount</div>
                                <div>Status</div>
                            </div>
                        `;

                        data.transactions.forEach(transaction => {
                            tableHTML += `
                                <div class="transaction-item">
                                    <div>${new Date(transaction.created_at).toLocaleDateString()}</div>
                                    <div>${transaction.plan_name || transaction.description}</div>
                                    <div>KSH ${new Intl.NumberFormat().format(transaction.amount)}</div>
                                    <div><span class="transaction-status ${transaction.status}">${transaction.status}</span></div>
                                </div>
                            `;
                        });

                        tableContainer.innerHTML = tableHTML;
                    } else {
                        tableContainer.innerHTML = '<div class="loading">No payment history found.</div>';
                    }
                } else {
                    tableContainer.innerHTML = '<div class="loading">Failed to load payment history.</div>';
                }

            } catch (error) {
                console.error('Payment history error:', error);
                tableContainer.innerHTML = '<div class="loading">Error loading payment history.</div>';
            }
        }

        // Download payment history
        function downloadPaymentHistory() {
            // Create a simple CSV download
            window.open('../api/profile.php?action=download_payment_history&user_id=<?php echo $user_id; ?>', '_blank');
        }
    </script>

    <script src="dashboard.js"></script>
</body>
</html>
