// Admin Login JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Check if admin is already logged in
    checkAdminSession();

    // Form elements
    const loginForm = document.getElementById('adminLoginForm');
    const emailInput = document.getElementById('adminEmail');
    const passwordInput = document.getElementById('adminPassword');
    const togglePassword = document.getElementById('togglePassword');
    const loginBtn = document.getElementById('loginBtn');
    const loginAlert = document.getElementById('loginAlert');
    
    // Password toggle functionality
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            if (type === 'password') {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            } else {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            }
        });
    }
    
    // Form submission
    if (loginForm) {
        loginForm.addEventListener('submit', handleAdminLogin);
    }
    
    // Auto-focus email input
    if (emailInput) {
        emailInput.focus();
    }
    
    // Enter key handling
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && (emailInput === document.activeElement || passwordInput === document.activeElement)) {
            e.preventDefault();
            loginForm.dispatchEvent(new Event('submit'));
        }
    });
});

// API base URL - using real admin auth
const API_BASE_URL = 'admin-auth.php';

async function handleAdminLogin(e) {
    e.preventDefault();

    const email = document.getElementById('adminEmail').value.trim();
    const password = document.getElementById('adminPassword').value;
    const rememberMe = document.getElementById('rememberMe').checked;

    // Validation
    if (!email || !password) {
        showAlert('Please fill in all fields', 'error');
        return;
    }

    if (!isValidEmail(email)) {
        showAlert('Please enter a valid email address', 'error');
        return;
    }

    // Show loading state
    setLoadingState(true);
    hideAlert();

    try {
        // Send login request to PHP backend
        const response = await fetch(API_BASE_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: email,
                password: password,
                rememberMe: rememberMe
            })
        });

        const data = await response.json();

        if (data.success) {
            // Store admin info and token in localStorage for client-side access
            localStorage.setItem('adminInfo', JSON.stringify(data.admin));
            localStorage.setItem('adminToken', data.admin.token);

            // Show success message
            showAlert('Login successful! Redirecting to admin dashboard...', 'success');

            // Redirect to admin dashboard
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 2000);

        } else {
            let errorMessage = data.message || 'Invalid admin credentials. Please check your email and password.';
            if (data.debug) {
                errorMessage += '\nDebug: ' + data.debug;
                console.error('Login debug info:', data);
            }
            showAlert(errorMessage, 'error');
        }

    } catch (error) {
        console.error('Login error:', error);
        showAlert('An error occurred during login. Please try again.', 'error');
    } finally {
        setLoadingState(false);
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function setLoadingState(loading) {
    const loginBtn = document.getElementById('loginBtn');
    const btnText = loginBtn.querySelector('.btn-text');
    const btnLoader = loginBtn.querySelector('.btn-loader');
    
    if (loading) {
        loginBtn.disabled = true;
        btnText.style.display = 'none';
        btnLoader.style.display = 'flex';
    } else {
        loginBtn.disabled = false;
        btnText.style.display = 'block';
        btnLoader.style.display = 'none';
    }
}

function showAlert(message, type) {
    const alert = document.getElementById('loginAlert');
    const alertIcon = alert.querySelector('.alert-icon');
    const alertMessage = alert.querySelector('.alert-message');
    
    // Set alert content
    alertMessage.textContent = message;
    
    // Set alert type and icon
    alert.className = `alert ${type}`;
    
    if (type === 'success') {
        alertIcon.className = 'alert-icon fas fa-check-circle';
    } else if (type === 'error') {
        alertIcon.className = 'alert-icon fas fa-exclamation-circle';
    } else {
        alertIcon.className = 'alert-icon fas fa-info-circle';
    }
    
    // Show alert
    alert.style.display = 'block';
    
    // Auto-hide error alerts after 5 seconds
    if (type === 'error') {
        setTimeout(() => {
            hideAlert();
        }, 5000);
    }
}

function hideAlert() {
    const alert = document.getElementById('loginAlert');
    alert.style.display = 'none';
}

// Check if admin is already logged in
async function checkAdminSession() {
    try {
        const response = await fetch(`${API_BASE_URL}?action=check`, {
            method: 'GET',
            credentials: 'same-origin'
        });

        const data = await response.json();

        if (data.success) {
            // Admin is already logged in, redirect to dashboard
            window.location.href = 'dashboard.html';
        } else {
            // Clear any stale localStorage data
            localStorage.removeItem('adminInfo');
        }
    } catch (error) {
        console.error('Session check error:', error);
        // Clear any stale localStorage data
        localStorage.removeItem('adminInfo');
    }
}

// Add some demo functionality
window.fillDemoCredentials = function() {
    document.getElementById('adminEmail').value = '<EMAIL>';
    document.getElementById('adminPassword').value = 'admin@123';
    showAlert('Demo credentials filled! Click "Sign In" to continue.', 'success');
};

// Add click handler for demo credentials
document.addEventListener('click', function(e) {
    if (e.target.closest('.demo-credentials')) {
        fillDemoCredentials();
    }
});
