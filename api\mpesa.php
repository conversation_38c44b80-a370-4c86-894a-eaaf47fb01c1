<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// M-Pesa Configuration (SANDBOX)
$CONSUMER_KEY = '5VmZidGWUEgAToibaggZHGfmZ8CCCdM258Ho9xAtPO8Tp3hW';
$CONSUMER_SECRET = 'kAMWAa1xUqotdjjoMflWmwhXBRGalO9whVkg53T6AaJq7pE9E1kN79S1maGJDhA9';
$BUSINESS_SHORT_CODE = '174379';
$PASSKEY = 'bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919';
$CALLBACK_URL = 'http://localhost/Forex/api/callback.php';

// M-Pesa API URLs (SANDBOX)
$ACCESS_TOKEN_URL = 'https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials';
$STK_PUSH_URL = 'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest';

// Function to verify user session
function verifyUserSession() {
    $headers = getallheaders();
    $token = null;

    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    }

    if (!$token) {
        sendJsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
    }

    try {
        $pdo = getDBConnection();

        $stmt = $pdo->prepare("
            SELECT u.*, us.expires_at
            FROM users u
            JOIN user_sessions us ON u.id = us.user_id
            WHERE us.token = ? AND us.expires_at > ? AND u.status = 'active'
        ");
        $stmt->execute([$token, date('Y-m-d H:i:s')]);
        $user = $stmt->fetch();

        if (!$user) {
            sendJsonResponse(['success' => false, 'message' => 'Invalid or expired session'], 401);
        }

        return $user;
    } catch (Exception $e) {
        logError("Session verification error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Authentication failed'], 500);
    }
}

function getAccessToken() {
    global $CONSUMER_KEY, $CONSUMER_SECRET, $ACCESS_TOKEN_URL;

    $credentials = base64_encode($CONSUMER_KEY . ':' . $CONSUMER_SECRET);
    $url = $ACCESS_TOKEN_URL;
    
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_HTTPHEADER, [
        'Authorization: Basic ' . $credentials,
        'Content-Type: application/json'
    ]);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    if ($httpCode == 200) {
        $result = json_decode($response, true);
        return $result['access_token'] ?? null;
    }
    
    return null;
}

function handleSTKPush() {
    global $BUSINESS_SHORT_CODE, $PASSKEY, $CALLBACK_URL, $STK_PUSH_URL;

    // Get input data
    $input = json_decode(file_get_contents('php://input'), true);

    // Validate input - check for both parameter formats
    $phoneNumber = $input['phone'] ?? $input['phoneNumber'] ?? null;
    $amount = $input['amount'] ?? null;

    if (!$phoneNumber || !$amount) {
        sendJsonResponse(['success' => false, 'message' => 'Phone number and amount are required'], 400);
        return;
    }

    $amount = (int)$amount;
    $subscriptionId = $input['subscription_id'] ?? 0;
    $planType = $input['plan_type'] ?? '';
    $billingCycle = $input['billing_cycle'] ?? 'monthly';
    $userId = $input['user_id'] ?? null;

    // Get user ID from session if not provided
    if (!$userId && isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
    }

    if (!$userId) {
        sendJsonResponse(['success' => false, 'message' => 'User ID required'], 400);
        return;
    }

    // Validate phone number
    if (!preg_match('/^254[0-9]{9}$/', $phoneNumber)) {
        sendJsonResponse(['success' => false, 'message' => 'Phone number must be in format 254XXXXXXXXX'], 400);
        return;
    }

    // Validate amount
    if ($amount <= 0) {
        sendJsonResponse(['success' => false, 'message' => 'Amount must be a positive number'], 400);
        return;
    }

    try {
        $pdo = getDBConnection();

        // Get user details
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if (!$user) {
            sendJsonResponse(['success' => false, 'message' => 'User not found'], 404);
            return;
        }

        // If subscription_id is provided, verify it exists and belongs to user
        if ($subscriptionId > 0) {
            $stmt = $pdo->prepare("SELECT * FROM user_subscriptions WHERE id = ? AND user_id = ?");
            $stmt->execute([$subscriptionId, $user['id']]);
            $subscription = $stmt->fetch();

            if (!$subscription) {
                sendJsonResponse(['success' => false, 'message' => 'Subscription not found'], 404);
                return;
            }
        } else {
            // Create new subscription if not provided
            if (empty($planType)) {
                sendJsonResponse(['success' => false, 'message' => 'Plan type required for new subscription'], 400);
                return;
            }

            // Get plan details
            $stmt = $pdo->prepare("SELECT * FROM subscription_plans WHERE plan_type = ? AND is_active = TRUE");
            $stmt->execute([$planType]);
            $plan = $stmt->fetch();

            if (!$plan) {
                sendJsonResponse(['success' => false, 'message' => 'Plan not found'], 404);
                return;
            }

            // Verify amount matches plan price
            $expectedAmount = $billingCycle === 'yearly' ? $plan['price_yearly'] : $plan['price_monthly'];
            if ($amount != $expectedAmount) {
                sendJsonResponse(['success' => false, 'message' => 'Amount does not match plan price'], 400);
                return;
            }

            // Calculate end date
            $duration = $billingCycle === 'yearly' ? '+1 year' : '+1 month';
            $endDate = date('Y-m-d H:i:s', strtotime($duration));

            // Generate transaction ID
            $transactionId = 'TXN_' . time() . '_' . $user['id'];

            // Start transaction
            $pdo->beginTransaction();

            // Cancel any existing active subscriptions
            $stmt = $pdo->prepare("UPDATE user_subscriptions SET status = 'cancelled', cancelled_at = ? WHERE user_id = ? AND status = 'active'");
            $stmt->execute([date('Y-m-d H:i:s'), $user['id']]);

            // Create new subscription
            $stmt = $pdo->prepare("
                INSERT INTO user_subscriptions (user_id, plan_type, status, amount, currency, start_date, end_date, payment_method, transaction_id, created_at)
                VALUES (?, ?, 'pending', ?, 'KES', ?, ?, 'mpesa', ?, ?)
            ");
            $stmt->execute([
                $user['id'],
                $planType,
                $amount,
                date('Y-m-d H:i:s'),
                $endDate,
                $transactionId,
                date('Y-m-d H:i:s')
            ]);

            $subscriptionId = $pdo->lastInsertId();
            $pdo->commit();
        }

        $accessToken = getAccessToken();
        if (!$accessToken) {
            sendJsonResponse(['success' => false, 'message' => 'Failed to get access token'], 500);
            return;
        }

        // Generate password
        $timestamp = date('YmdHis');
        $password = base64_encode($BUSINESS_SHORT_CODE . $PASSKEY . $timestamp);

        $stkPushData = [
            'BusinessShortCode' => $BUSINESS_SHORT_CODE,
            'Password' => $password,
            'Timestamp' => $timestamp,
            'TransactionType' => 'CustomerPayBillOnline',
            'Amount' => $amount,
            'PartyA' => $phoneNumber,
            'PartyB' => $BUSINESS_SHORT_CODE,
            'PhoneNumber' => $phoneNumber,
            'CallBackURL' => $CALLBACK_URL,
            'AccountReference' => 'ForexClass',
            'TransactionDesc' => 'Subscription Payment - ' . ($planType ?? 'Plan')
        ];

        $url = $STK_PUSH_URL;

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $accessToken,
            'Content-Type: application/json'
        ]);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($stkPushData));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($httpCode == 200) {
            $result = json_decode($response, true);

            if (isset($result['ResponseCode']) && $result['ResponseCode'] === '0') {
                // Store payment request in database
                $stmt = $pdo->prepare("
                    INSERT INTO mpesa_payments (
                        user_id, subscription_id, checkout_request_id, merchant_request_id,
                        phone_number, amount, status, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, 'pending', ?)
                ");
                $stmt->execute([
                    $user['id'],
                    $subscriptionId,
                    $result['CheckoutRequestID'],
                    $result['MerchantRequestID'],
                    $phoneNumber,
                    $amount,
                    date('Y-m-d H:i:s')
                ]);

                sendJsonResponse([
                    'success' => true,
                    'message' => 'STK Push sent successfully. Please check your phone.',
                    'checkout_request_id' => $result['CheckoutRequestID'],
                    'merchant_request_id' => $result['MerchantRequestID'],
                    'subscription_id' => $subscriptionId,
                    'customer_message' => $result['CustomerMessage']
                ]);
            } else {
                sendJsonResponse([
                    'success' => false,
                    'message' => $result['ResponseDescription'] ?? 'STK Push failed',
                    'error_code' => $result['ResponseCode'] ?? 'unknown'
                ]);
            }
        } else {
            sendJsonResponse([
                'success' => false,
                'message' => 'Failed to initiate payment. Please try again.'
            ], 500);
        }

    } catch (Exception $e) {
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        logError("STK Push error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Payment initiation failed'], 500);
    }
}

// Check payment status
function checkPaymentStatus() {
    $user = verifyUserSession();
    $checkoutRequestId = $_GET['checkout_request_id'] ?? '';

    if (empty($checkoutRequestId)) {
        sendJsonResponse(['success' => false, 'message' => 'Checkout request ID required'], 400);
        return;
    }

    try {
        $pdo = getDBConnection();

        // Get payment record
        $stmt = $pdo->prepare("
            SELECT mp.*, us.plan_type, us.status as subscription_status
            FROM mpesa_payments mp
            LEFT JOIN user_subscriptions us ON mp.subscription_id = us.id
            WHERE mp.checkout_request_id = ? AND mp.user_id = ?
        ");
        $stmt->execute([$checkoutRequestId, $user['id']]);
        $payment = $stmt->fetch();

        if (!$payment) {
            sendJsonResponse(['success' => false, 'message' => 'Payment record not found'], 404);
            return;
        }

        sendJsonResponse([
            'success' => true,
            'payment_status' => $payment['status'],
            'amount' => $payment['amount'],
            'phone_number' => $payment['phone_number'],
            'subscription_status' => $payment['subscription_status'],
            'plan_type' => $payment['plan_type'],
            'mpesa_receipt_number' => $payment['mpesa_receipt_number'],
            'created_at' => $payment['created_at'],
            'completed_at' => $payment['completed_at']
        ]);

    } catch (Exception $e) {
        logError("Payment status check error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Status check failed'], 500);
    }
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

switch ($method) {
    case 'POST':
        if ($action === 'stk-push' || empty($action)) {
            // Handle STK push for both /api/mpesa.php and /api/mpesa.php?action=stk-push
            handleSTKPush();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;

    case 'GET':
        if ($action === 'status') {
            checkPaymentStatus();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;

    default:
        sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
        break;
}
?>
