-- Admin Setup SQL
-- This script sets up the admin user with the specified credentials
-- Email: <EMAIL>
-- Password: admin@123

USE forex_class;

-- First, remove any existing admin user with the old email
DELETE FROM users WHERE email = '<EMAIL>' AND role = 'admin';

-- Insert the new admin user with the correct credentials
-- Password hash for 'admin@123' using PHP password_hash()
INSERT INTO users (name, email, phone, password, role, email_verified, status, created_at) VALUES 
('Admin User', '<EMAIL>', '254700000000', '$2y$10$8K1p/a0dhrxSUt3LxjCa.eL9/Fve9QF4zzVdHI4S3KdDot7kWo0EG', 'admin', TRUE, 'active', NOW())
ON DUPLICATE KEY UPDATE 
    password = '$2y$10$8K1p/a0dhrxSUt3LxjCa.eL9/Fve9QF4zzVdHI4S3KdDot7kWo0EG',
    role = 'admin',
    email_verified = TRUE,
    status = 'active';

-- Verify the admin user was created
SELECT id, name, email, role, status, email_verified, created_at 
FROM users 
WHERE email = '<EMAIL>' AND role = 'admin';
