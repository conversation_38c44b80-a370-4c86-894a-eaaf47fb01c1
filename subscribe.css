/* Subscribe Page Styles */
.subscribe-hero {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    color: #fff;
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.subscribe-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
    animation: patternMove 20s ease-in-out infinite;
}

.hero-content h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Billing Toggle */
.billing-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.billing-label {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
}

.discount-badge {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: #fff;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 700;
    margin-left: 0.5rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: 0.4s;
    border-radius: 30px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4f46e5;
}

input:checked + .slider:before {
    transform: translateX(30px);
}

/* Pricing Plans */
.pricing-plans {
    padding: 80px 0;
    background: #f8fafc;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.plan-card {
    background: #fff;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid transparent;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.plan-card.popular {
    border-color: #4f46e5;
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: #fff;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.plan-header {
    text-align: center;
    margin-bottom: 2rem;
}

.plan-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: #fff;
}

.plan-card.basic .plan-icon {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.plan-card.premium .plan-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.plan-card.vip .plan-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.plan-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.plan-header p {
    color: #64748b;
    font-size: 0.95rem;
}

/* Plan Pricing */
.plan-pricing {
    text-align: center;
    margin-bottom: 2rem;
}

.price-display {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.currency {
    font-size: 1.2rem;
    font-weight: 600;
    color: #64748b;
}

.amount {
    font-size: 3rem;
    font-weight: 800;
    color: #1e293b;
    margin: 0 0.25rem;
}

.period {
    font-size: 1rem;
    color: #64748b;
    font-weight: 500;
}

.yearly-savings {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.original-price {
    text-decoration: line-through;
    color: #94a3b8;
    font-size: 0.875rem;
}

.savings {
    color: #22c55e;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Plan Features */
.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
    font-size: 0.95rem;
}

.plan-features li:last-child {
    border-bottom: none;
}

.plan-features .fa-check {
    color: #22c55e;
    font-size: 0.875rem;
}

.plan-features .fa-times {
    color: #ef4444;
    font-size: 0.875rem;
}

/* Plan Button */
.plan-btn {
    width: 100%;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.plan-card.basic .plan-btn {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: #fff;
}

.plan-card.premium .plan-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: #fff;
}

.plan-card.vip .plan-btn {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: #fff;
}

.plan-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Features Comparison */
.features-comparison {
    padding: 80px 0;
    background: #fff;
}

.features-comparison h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 3rem;
}

.comparison-table {
    overflow-x: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.comparison-table table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
}

.comparison-table th,
.comparison-table td {
    padding: 1.5rem;
    text-align: center;
    border-bottom: 1px solid #e2e8f0;
}

.comparison-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #1e293b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.comparison-table td:first-child {
    text-align: left;
    font-weight: 500;
    color: #374151;
}

.comparison-table .fa-check {
    color: #22c55e;
    font-size: 1.25rem;
}

.comparison-table .fa-times {
    color: #ef4444;
    font-size: 1.25rem;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: #f8fafc;
}

.faq-section h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 3rem;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.faq-item {
    background: #fff;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.faq-item h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.faq-item p {
    color: #64748b;
    line-height: 1.6;
}
