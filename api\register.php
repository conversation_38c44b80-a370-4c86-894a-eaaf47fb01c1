<?php
/**
 * User Registration API
 *
 * Handles user registration with validation and security features
 *
 * <AUTHOR> Team
 * @version 1.0
 */

require_once 'config.php';

// Set CORS headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($input['name']) || !isset($input['email']) || !isset($input['phone']) || !isset($input['password'])) {
    sendJsonResponse(['success' => false, 'message' => 'All fields are required'], 400);
}

$name = trim($input['name']);
$email = filter_var($input['email'], FILTER_SANITIZE_EMAIL);
$phone = trim($input['phone']);
$password = $input['password'];

// Validate input
if (empty($name) || strlen($name) < 2) {
    sendJsonResponse(['success' => false, 'message' => 'Name must be at least 2 characters long'], 400);
}

if (!validateEmail($email)) {
    sendJsonResponse(['success' => false, 'message' => 'Invalid email format'], 400);
}

if (!validatePhone($phone)) {
    sendJsonResponse(['success' => false, 'message' => 'Invalid phone number format. Use 254XXXXXXXXX'], 400);
}

if (strlen($password) < 6) {
    sendJsonResponse(['success' => false, 'message' => 'Password must be at least 6 characters long'], 400);
}

try {
    // Connect to database
    $pdo = getDBConnection();
    
    // Check if email already exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        sendJsonResponse(['success' => false, 'message' => 'Email already registered'], 409);
    }

    // Check if phone already exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = ?");
    $stmt->execute([$phone]);
    if ($stmt->fetch()) {
        sendJsonResponse(['success' => false, 'message' => 'Phone number already registered'], 409);
    }
    
    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // Generate email verification token
    $verification_token = generateToken();
    
    // Insert user (auto-verify email for testing)
    $stmt = $pdo->prepare("
        INSERT INTO users (name, email, phone, password, role, verification_token, email_verified, status, created_at)
        VALUES (?, ?, ?, ?, 'user', ?, TRUE, 'active', ?)
    ");

    $stmt->execute([$name, $email, $phone, $hashed_password, $verification_token, date('Y-m-d H:i:s')]);
    $user_id = $pdo->lastInsertId();
    
    // Send verification email (you can implement this later)
    // sendVerificationEmail($email, $name, $verification_token);
    
    // Return success response
    sendJsonResponse([
        'success' => true,
        'message' => 'Account created successfully! Please check your email for verification.',
        'user_id' => $user_id
    ], 201);
    
} catch (PDOException $e) {
    logError("Registration database error: " . $e->getMessage(), ['email' => $email, 'phone' => $phone]);

    // Check for duplicate entry error
    if ($e->getCode() == 23000) {
        sendJsonResponse(['success' => false, 'message' => 'Email or phone number already registered'], 409);
    } else {
        sendJsonResponse(['success' => false, 'message' => 'Database error occurred'], 500);
    }
} catch (Exception $e) {
    logError("Registration error: " . $e->getMessage(), ['email' => $email, 'phone' => $phone]);
    sendJsonResponse(['success' => false, 'message' => 'An error occurred'], 500);
}

// Function to send verification email (implement as needed)
function sendVerificationEmail($email, $name, $token) {
    // You can implement email sending here using PHPMailer or similar
    // For now, we'll just log it
    error_log("Verification email should be sent to: $email with token: $token");
}
?>
