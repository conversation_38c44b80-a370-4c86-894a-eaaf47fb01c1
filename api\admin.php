<?php
/**
 * Admin API Endpoint
 * 
 * Handles admin operations for subscription plans, users, and platform management
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

session_start();
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    sendJsonResponse(['success' => false, 'message' => 'Unauthorized'], 401);
}

// Send JSON response
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit();
}

// Log admin actions
function logAdminAction($action, $details = []) {
    $logFile = __DIR__ . '/logs/admin_' . date('Y-m-d') . '.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = date('Y-m-d H:i:s') . ' - Admin ID: ' . $_SESSION['admin_id'] . ' - Action: ' . $action . ' - Details: ' . json_encode($details) . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_GET['action'] ?? '';

switch ($action) {
    case 'create_plan':
        createSubscriptionPlan($input);
        break;
        
    case 'update_plan':
        updateSubscriptionPlan($input);
        break;
        
    case 'delete_plan':
        deleteSubscriptionPlan($input);
        break;
        
    case 'get_plan':
        getSubscriptionPlan($_GET['id'] ?? '');
        break;
        
    case 'get_plans':
        getSubscriptionPlans();
        break;
        
    default:
        sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        break;
}

function createSubscriptionPlan($data) {
    try {
        $pdo = getDBConnection();
        
        // Validate required fields
        $required = ['name', 'plan_type', 'description', 'price_monthly', 'price_yearly'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                sendJsonResponse(['success' => false, 'message' => "Field '$field' is required"], 400);
            }
        }
        
        // Check if plan type already exists
        $stmt = $pdo->prepare("SELECT id FROM subscription_plans WHERE plan_type = ?");
        $stmt->execute([$data['plan_type']]);
        if ($stmt->fetch()) {
            sendJsonResponse(['success' => false, 'message' => 'Plan type already exists'], 400);
        }
        
        // Insert new plan
        $stmt = $pdo->prepare("
            INSERT INTO subscription_plans 
            (name, plan_type, description, price_monthly, price_yearly, features, telegram_access, signal_access, is_active, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $features = json_encode($data['features'] ?? []);
        $telegramAccess = $data['telegram_access'] ?? 0;
        $signalAccess = $data['signal_access'] ?? 0;
        $isActive = $data['is_active'] ?? 1;
        
        $stmt->execute([
            $data['name'],
            $data['plan_type'],
            $data['description'],
            $data['price_monthly'],
            $data['price_yearly'],
            $features,
            $telegramAccess,
            $signalAccess,
            $isActive
        ]);
        
        $planId = $pdo->lastInsertId();
        
        logAdminAction('create_plan', [
            'plan_id' => $planId,
            'plan_type' => $data['plan_type'],
            'name' => $data['name']
        ]);
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Subscription plan created successfully',
            'plan_id' => $planId
        ]);
        
    } catch (Exception $e) {
        logAdminAction('create_plan_error', ['error' => $e->getMessage()]);
        sendJsonResponse(['success' => false, 'message' => 'Failed to create plan: ' . $e->getMessage()], 500);
    }
}

function updateSubscriptionPlan($data) {
    try {
        $pdo = getDBConnection();
        
        if (empty($data['plan_id'])) {
            sendJsonResponse(['success' => false, 'message' => 'Plan ID is required'], 400);
        }
        
        // Check if plan exists
        $stmt = $pdo->prepare("SELECT id FROM subscription_plans WHERE id = ?");
        $stmt->execute([$data['plan_id']]);
        if (!$stmt->fetch()) {
            sendJsonResponse(['success' => false, 'message' => 'Plan not found'], 404);
        }
        
        // Update plan
        $stmt = $pdo->prepare("
            UPDATE subscription_plans 
            SET name = ?, plan_type = ?, description = ?, price_monthly = ?, price_yearly = ?, 
                features = ?, telegram_access = ?, signal_access = ?, is_active = ?, updated_at = NOW()
            WHERE id = ?
        ");
        
        $features = json_encode($data['features'] ?? []);
        $telegramAccess = $data['telegram_access'] ?? 0;
        $signalAccess = $data['signal_access'] ?? 0;
        $isActive = $data['is_active'] ?? 1;
        
        $stmt->execute([
            $data['name'],
            $data['plan_type'],
            $data['description'],
            $data['price_monthly'],
            $data['price_yearly'],
            $features,
            $telegramAccess,
            $signalAccess,
            $isActive,
            $data['plan_id']
        ]);
        
        logAdminAction('update_plan', [
            'plan_id' => $data['plan_id'],
            'plan_type' => $data['plan_type'],
            'name' => $data['name']
        ]);
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Subscription plan updated successfully'
        ]);
        
    } catch (Exception $e) {
        logAdminAction('update_plan_error', ['error' => $e->getMessage()]);
        sendJsonResponse(['success' => false, 'message' => 'Failed to update plan: ' . $e->getMessage()], 500);
    }
}

function deleteSubscriptionPlan($data) {
    try {
        $pdo = getDBConnection();
        
        if (empty($data['plan_id'])) {
            sendJsonResponse(['success' => false, 'message' => 'Plan ID is required'], 400);
        }
        
        // Check if plan has active subscriptions
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM user_subscriptions us
            JOIN subscription_plans sp ON us.plan_type = sp.plan_type
            WHERE sp.id = ? AND us.status = 'active' AND us.end_date > NOW()
        ");
        $stmt->execute([$data['plan_id']]);
        $activeCount = $stmt->fetch()['count'];
        
        if ($activeCount > 0) {
            sendJsonResponse(['success' => false, 'message' => 'Cannot delete plan with active subscriptions'], 400);
        }
        
        // Delete plan
        $stmt = $pdo->prepare("DELETE FROM subscription_plans WHERE id = ?");
        $stmt->execute([$data['plan_id']]);
        
        logAdminAction('delete_plan', ['plan_id' => $data['plan_id']]);
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Subscription plan deleted successfully'
        ]);
        
    } catch (Exception $e) {
        logAdminAction('delete_plan_error', ['error' => $e->getMessage()]);
        sendJsonResponse(['success' => false, 'message' => 'Failed to delete plan: ' . $e->getMessage()], 500);
    }
}

function getSubscriptionPlan($planId) {
    try {
        $pdo = getDBConnection();
        
        if (empty($planId)) {
            sendJsonResponse(['success' => false, 'message' => 'Plan ID is required'], 400);
        }
        
        $stmt = $pdo->prepare("SELECT * FROM subscription_plans WHERE id = ?");
        $stmt->execute([$planId]);
        $plan = $stmt->fetch();
        
        if (!$plan) {
            sendJsonResponse(['success' => false, 'message' => 'Plan not found'], 404);
        }
        
        sendJsonResponse([
            'success' => true,
            'plan' => $plan
        ]);
        
    } catch (Exception $e) {
        sendJsonResponse(['success' => false, 'message' => 'Failed to get plan: ' . $e->getMessage()], 500);
    }
}

function getSubscriptionPlans() {
    try {
        $pdo = getDBConnection();
        
        $stmt = $pdo->query("SELECT * FROM subscription_plans ORDER BY price_monthly ASC");
        $plans = $stmt->fetchAll();
        
        sendJsonResponse([
            'success' => true,
            'plans' => $plans
        ]);
        
    } catch (Exception $e) {
        sendJsonResponse(['success' => false, 'message' => 'Failed to get plans: ' . $e->getMessage()], 500);
    }
}
?>
