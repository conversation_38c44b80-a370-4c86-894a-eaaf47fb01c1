// Admin Dashboard JavaScript
const API_BASE_URL = '../api/admin-dashboard.php';
const PACKAGES_API_URL = '../api/admin-packages.php';

// Function to test API URLs and find working one
async function findWorkingAPI() {
    console.log('Testing API URLs to find working one...');

    for (const url of API_URLS) {
        try {
            console.log(`Testing API URL: ${url}`);
            const response = await fetch(`${url}?action=overview`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    console.log(`✅ Working API URL found: ${url}`);
                    API_BASE_URL = url;
                    PACKAGES_API_URL = url;
                    return url;
                }
            }
        } catch (error) {
            console.log(`❌ API URL failed: ${url} - ${error.message}`);
        }
    }

    console.error('❌ No working API URL found!');
    return null;
}

document.addEventListener('DOMContentLoaded', function() {
    // Check admin session first
    checkAdminSession();

    // Initialize admin dashboard
    initializeAdminDashboard();
    
    // Mobile sidebar toggle
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 1024) {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('active');
                }
            }
        });
    }
    
    // Navigation handling
    const navLinks = document.querySelectorAll('.nav-link[data-page]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.getAttribute('data-page');
            navigateToPage(page);
            
            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            this.closest('.nav-item').classList.add('active');
            
            // Close mobile sidebar
            if (window.innerWidth <= 1024) {
                sidebar.classList.remove('active');
            }
        });
    });
    
    // Exchange rate refresh
    const refreshRate = document.getElementById('refreshRate');
    if (refreshRate) {
        refreshRate.addEventListener('click', function() {
            this.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                this.style.transform = 'rotate(0deg)';
                updateExchangeRate();
            }, 500);
        });
    }
    
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleLogout();
        });
    }
    
    // Transaction action buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-action')) {
            const action = e.target.classList.contains('approve') ? 'approve' : 'reject';
            const row = e.target.closest('tr');
            const transactionId = row.querySelector('.transaction-id').textContent;
            handleTransactionAction(transactionId, action);
        }
    });
    
    // Search functionality
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterTransactions(this.value);
        });
    }
    
    // Filter functionality
    const filterSelect = document.querySelector('.filter-select');
    if (filterSelect) {
        filterSelect.addEventListener('change', function() {
            filterTransactionsByStatus(this.value);
        });
    }
    
    // Pagination
    const paginationBtns = document.querySelectorAll('.pagination-btn');
    paginationBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            if (!this.disabled && !this.classList.contains('active')) {
                handlePagination(this.textContent);
            }
        });
    });

    // Subscription modal handling
    const subscriptionModal = document.getElementById('subscriptionModal');
    const closeSubscriptionModal = document.getElementById('closeSubscriptionModal');
    const cancelSubscription = document.getElementById('cancelSubscription');
    const subscriptionForm = document.getElementById('subscriptionForm');

    // Open subscription modal when clicking "Add Subscription" button
    document.addEventListener('click', function(e) {
        if (e.target.textContent === 'Add Subscription') {
            openSubscriptionModal();
        }
    });

    if (closeSubscriptionModal) {
        closeSubscriptionModal.addEventListener('click', closeSubscriptionModalHandler);
    }

    if (cancelSubscription) {
        cancelSubscription.addEventListener('click', closeSubscriptionModalHandler);
    }

    if (subscriptionModal) {
        subscriptionModal.addEventListener('click', function(e) {
            if (e.target === subscriptionModal) {
                closeSubscriptionModalHandler();
            }
        });
    }

    if (subscriptionForm) {
        subscriptionForm.addEventListener('submit', handleSubscriptionSubmit);
    }

    // Plan and duration change handlers
    const planSelect = document.getElementById('planSelect');
    const durationSelect = document.getElementById('duration');
    const amountInput = document.getElementById('amount');

    if (planSelect && durationSelect && amountInput) {
        planSelect.addEventListener('change', calculateAmount);
        durationSelect.addEventListener('change', calculateAmount);
    }
});

function initializeAdminDashboard() {
    console.log('Initializing admin dashboard...'); // Debug log

    // Get admin info from localStorage (set during login)
    const adminInfo = localStorage.getItem('adminInfo');
    const adminToken = localStorage.getItem('adminToken');

    console.log('Admin info:', adminInfo);
    console.log('Admin token:', adminToken ? 'Present' : 'Missing');

    if (adminInfo) {
        const admin = JSON.parse(adminInfo);
        // Update admin info in UI
        const userNameElement = document.getElementById('userName');
        if (userNameElement) {
            userNameElement.textContent = admin.name;
        }
    }

    // Load initial data
    showDashboardPage();
    loadDashboardData();
}

// Load real dashboard data from API
async function loadDashboardData() {
    try {
        const token = localStorage.getItem('adminToken');
        if (!token) {
            console.log('No admin token found, redirecting to login');
            window.location.href = 'index.html';
            return;
        }

        const response = await fetch(`${API_BASE_URL}?action=overview`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            updateDashboardStats(data.data.overview);
            updateSubscriptionBreakdown(data.data.subscription_breakdown);
            updateRecentTransactions(data.data.recent_transactions);
            updateCharts(data.data.charts);
        } else {
            console.error('Failed to load dashboard data:', data.message);
            alert('Failed to load dashboard data: ' + data.message);
        }

    } catch (error) {
        console.error('Dashboard data error:', error);
        alert('Network error loading dashboard data: ' + error.message);
    }
}

// Simple alert function for now
function showAlert(message, type) {
    alert(message);
}

// Update dashboard statistics
function updateDashboardStats(stats) {
    console.log('Updating dashboard stats:', stats); // Debug log

    // Update stat cards
    const statElements = {
        'totalUsers': stats.total_users,
        'newUsersThisMonth': stats.new_users_this_month,
        'activeSubscriptions': stats.active_subscriptions,
        'monthlyRevenue': `KSH ${stats.monthly_revenue.toLocaleString()}`,
        'totalRevenue': `KSH ${stats.total_revenue.toLocaleString()}`
    };

    Object.entries(statElements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            console.log(`Updating ${id} with value:`, value);
            element.textContent = value;
        } else {
            console.log(`Element not found: ${id}`);
        }
    });

    // Update stat cards in the HTML
    updateStatCard('users-stat', stats.total_users, stats.new_users_this_month);
    updateStatCard('subscriptions-stat', stats.active_subscriptions, 0);
    updateStatCard('revenue-stat', stats.monthly_revenue, stats.total_revenue);
}

function updateStatCard(cardId, mainValue, subValue) {
    const card = document.querySelector(`[data-stat="${cardId}"]`);
    if (card) {
        const mainElement = card.querySelector('.stat-value');
        const subElement = card.querySelector('.stat-change');

        if (mainElement) {
            if (cardId === 'revenue-stat') {
                mainElement.textContent = `KSH ${mainValue.toLocaleString()}`;
            } else {
                mainElement.textContent = mainValue.toLocaleString();
            }
        }

        if (subElement && subValue !== undefined) {
            if (cardId === 'users-stat') {
                subElement.textContent = `+${subValue} this month`;
            } else if (cardId === 'revenue-stat') {
                subElement.textContent = `Total: KSH ${subValue.toLocaleString()}`;
            }
        }
    }
}

// Update subscription breakdown
function updateSubscriptionBreakdown(breakdown) {
    const container = document.querySelector('.subscription-breakdown');
    if (!container) return;

    if (breakdown.length === 0) {
        container.innerHTML = '<p>No active subscriptions found</p>';
        return;
    }

    const total = breakdown.reduce((sum, item) => sum + parseInt(item.count), 0);

    container.innerHTML = breakdown.map(item => {
        const percentage = total > 0 ? ((item.count / total) * 100).toFixed(1) : 0;
        return `
            <div class="subscription-item">
                <div class="subscription-info">
                    <span class="plan-name">${item.plan_name}</span>
                    <span class="plan-count">${item.count} subscribers</span>
                </div>
                <div class="subscription-bar">
                    <div class="bar-fill ${item.plan_type}" style="width: ${percentage}%"></div>
                </div>
                <span class="subscription-percentage">${percentage}%</span>
            </div>
        `;
    }).join('');
}

// Update recent transactions
function updateRecentTransactions(transactions) {
    const tbody = document.querySelector('#transactionsTable tbody');
    if (!tbody) return;

    if (transactions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6">No transactions found</td></tr>';
        return;
    }

    tbody.innerHTML = transactions.map(transaction => `
        <tr>
            <td>
                <div class="transaction-id">${transaction.transaction_id || transaction.id}</div>
                <div class="transaction-date">${new Date(transaction.created_at).toLocaleDateString()}</div>
            </td>
            <td>
                <div class="user-info">
                    <div class="user-name">${transaction.user_name}</div>
                    <div class="user-email">${transaction.user_email}</div>
                </div>
            </td>
            <td>
                <span class="plan-badge ${transaction.plan_type || 'unknown'}">${transaction.plan_type || 'N/A'}</span>
            </td>
            <td>KSH ${parseFloat(transaction.amount).toLocaleString()}</td>
            <td>
                <span class="status-badge ${transaction.status}">${transaction.status}</span>
            </td>
            <td>
                <button class="btn btn-sm btn-secondary" onclick="viewTransaction('${transaction.id}')">
                    View
                </button>
            </td>
        </tr>
    `).join('');
}

// Update charts (placeholder for now)
function updateCharts(chartData) {
    // This would integrate with Chart.js or similar library
    console.log('Chart data received:', chartData);

    // For now, just update some basic chart info
    if (chartData.monthly_revenue && chartData.monthly_revenue.length > 0) {
        const latestRevenue = chartData.monthly_revenue[chartData.monthly_revenue.length - 1];
        console.log('Latest monthly revenue:', latestRevenue);
    }

    if (chartData.user_growth && chartData.user_growth.length > 0) {
        const latestGrowth = chartData.user_growth[chartData.user_growth.length - 1];
        console.log('Latest user growth:', latestGrowth);
    }
}

// Package Management Functions
async function loadPackages() {
    try {
        const token = localStorage.getItem('adminToken');
        const response = await fetch(`${PACKAGES_API_URL}?action=list`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            updatePackagesTable(data.packages);
        } else {
            showAlert('Failed to load packages: ' + data.message, 'error');
        }

    } catch (error) {
        console.error('Load packages error:', error);
        showAlert('Network error loading packages', 'error');
    }
}

function updatePackagesTable(packages) {
    const tbody = document.querySelector('#packagesTable tbody');
    if (!tbody) return;

    if (packages.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7">No packages found</td></tr>';
        return;
    }

    tbody.innerHTML = packages.map(pkg => `
        <tr>
            <td>
                <div class="package-name">${pkg.name}</div>
                <div class="package-type">${pkg.plan_type}</div>
            </td>
            <td>${pkg.description}</td>
            <td>KSH ${parseFloat(pkg.price_monthly).toLocaleString()}</td>
            <td>KSH ${parseFloat(pkg.price_yearly).toLocaleString()}</td>
            <td>${pkg.active_subscribers || 0}</td>
            <td>
                <span class="status-badge ${pkg.is_active ? 'active' : 'inactive'}">
                    ${pkg.is_active ? 'Active' : 'Inactive'}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-primary" onclick="editPackage(${pkg.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deletePackage(${pkg.id})"
                            ${pkg.active_subscribers > 0 ? 'disabled title="Cannot delete package with active subscribers"' : ''}>
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

async function createPackage(packageData) {
    try {
        const token = localStorage.getItem('adminToken');
        const response = await fetch(`${PACKAGES_API_URL}?action=create`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(packageData)
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Package created successfully!', 'success');
            loadPackages(); // Reload packages table
            closePackageModal();
        } else {
            showAlert('Failed to create package: ' + data.message, 'error');
        }

    } catch (error) {
        console.error('Create package error:', error);
        showAlert('Network error creating package', 'error');
    }
}

async function updatePackage(packageId, packageData) {
    try {
        const token = localStorage.getItem('adminToken');
        const response = await fetch(`${PACKAGES_API_URL}?action=update`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: packageId, ...packageData })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Package updated successfully!', 'success');
            loadPackages(); // Reload packages table
            closePackageModal();
        } else {
            showAlert('Failed to update package: ' + data.message, 'error');
        }

    } catch (error) {
        console.error('Update package error:', error);
        showAlert('Network error updating package', 'error');
    }
}

async function deletePackage(packageId) {
    if (!confirm('Are you sure you want to delete this package? This action cannot be undone.')) {
        return;
    }

    try {
        const token = localStorage.getItem('adminToken');
        const response = await fetch(`${PACKAGES_API_URL}?action=delete&id=${packageId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Package deleted successfully!', 'success');
            loadPackages(); // Reload packages table
        } else {
            showAlert('Failed to delete package: ' + data.message, 'error');
        }

    } catch (error) {
        console.error('Delete package error:', error);
        showAlert('Network error deleting package', 'error');
    }
}

// Check admin session with backend
async function checkAdminSession() {
    try {
        const response = await fetch(`${API_BASE_URL}?action=check`, {
            method: 'GET',
            credentials: 'same-origin'
        });

        const data = await response.json();

        if (!data.success) {
            // Not authenticated, redirect to login
            window.location.href = 'index.html';
            return false;
        }

        // Store admin info for UI use
        localStorage.setItem('adminInfo', JSON.stringify(data.admin));
        return true;

    } catch (error) {
        console.error('Session check error:', error);
        // On error, redirect to login
        window.location.href = 'index.html';
        return false;
    }
}

function navigateToPage(page) {
    // Hide all pages
    document.querySelectorAll('.page-content').forEach(content => {
        content.style.display = 'none';
    });
    
    // Update page title and subtitle
    const pageTitle = document.getElementById('pageTitle');
    const pageSubtitle = document.getElementById('pageSubtitle');
    
    switch(page) {
        case 'dashboard':
            pageTitle.textContent = 'Dashboard Overview';
            pageSubtitle.textContent = 'Monitor your platform performance';
            showDashboardPage();
            break;
        case 'users':
            pageTitle.textContent = 'User Management';
            pageSubtitle.textContent = 'Manage platform users and permissions';
            showUsersPage();
            break;
        case 'subscriptions':
            pageTitle.textContent = 'Subscription Management';
            pageSubtitle.textContent = 'Manage user subscriptions and plans';
            showSubscriptionsPage();
            break;
        case 'packages':
            pageTitle.textContent = 'Package Management';
            pageSubtitle.textContent = 'Manage pricing plans and packages';
            showPackagesPage();
            break;
        case 'transactions':
            pageTitle.textContent = 'Transaction Management';
            pageSubtitle.textContent = 'Review payments and transaction history';
            showTransactionsPage();
            break;
        case 'telegram':
            pageTitle.textContent = 'Telegram Management';
            pageSubtitle.textContent = 'Manage Telegram channels and signals';
            showTelegramPage();
            break;
        case 'messages':
            pageTitle.textContent = 'Message Center';
            pageSubtitle.textContent = 'Send notifications and manage communications';
            showMessagesPage();
            break;
    }
}

function showTransactionsPage() {
    const transactionsPage = document.getElementById('transactionsPage');
    if (transactionsPage) {
        transactionsPage.style.display = 'block';
    }
}

function showDashboardPage() {
    const dashboardPage = document.getElementById('dashboardPage');
    if (dashboardPage) {
        dashboardPage.style.display = 'block';
        loadDashboardData(); // Use the new real data function
    }
}

// Old loadDashboardStats function removed - now using loadDashboardData() with real API data

function showUsersPage() {
    // Create users management page
    console.log('Showing users page');
}

function showSubscriptionsPage() {
    const subscriptionsPage = document.getElementById('subscriptionsPage');
    if (subscriptionsPage) {
        subscriptionsPage.style.display = 'block';
        loadSubscriptions();
    }
}

function showPackagesPage() {
    console.log('Showing packages page...'); // Debug log
    const packagesPage = document.getElementById('packagesPage');
    console.log('Packages page element:', packagesPage); // Debug log
    if (packagesPage) {
        packagesPage.style.display = 'block';
        console.log('Loading packages...'); // Debug log
        loadPackages();
    } else {
        console.error('Packages page element not found!');
    }
}

function showTelegramPage() {
    // Create telegram management page
    console.log('Showing telegram page');
}

function showMessagesPage() {
    // Create messages page
    console.log('Showing messages page');
}

function loadSubscriptions() {
    // In a real app, this would fetch from API
    const subscriptions = [
        {
            id: 1,
            user: { name: 'John Doe', email: '<EMAIL>' },
            plan: 'premium',
            status: 'active',
            startDate: '2024-01-15',
            endDate: '2024-02-15',
            amount: 5000
        },
        {
            id: 2,
            user: { name: 'Jane Smith', email: '<EMAIL>' },
            plan: 'vip',
            status: 'active',
            startDate: '2024-01-10',
            endDate: '2024-02-10',
            amount: 10000
        },
        {
            id: 3,
            user: { name: 'Mike Johnson', email: '<EMAIL>' },
            plan: 'basic',
            status: 'expired',
            startDate: '2023-12-15',
            endDate: '2024-01-15',
            amount: 2500
        }
    ];

    renderSubscriptions(subscriptions);
}

function renderSubscriptions(subscriptions) {
    const tbody = document.getElementById('subscriptionsTableBody');
    if (!tbody) return;

    tbody.innerHTML = subscriptions.map(subscription => `
        <tr data-subscription-id="${subscription.id}">
            <td>
                <div class="user-info-cell">
                    <span class="user-name">${subscription.user.name}</span>
                    <span class="user-email">${subscription.user.email}</span>
                </div>
            </td>
            <td>
                <span class="plan-badge ${subscription.plan}">${subscription.plan.charAt(0).toUpperCase() + subscription.plan.slice(1)}</span>
            </td>
            <td>
                <span class="status-badge ${subscription.status}">${subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}</span>
            </td>
            <td>
                <span class="date-text">${subscription.startDate}</span>
            </td>
            <td>
                <span class="date-text">${subscription.endDate}</span>
            </td>
            <td>
                <div class="amount-info">
                    <span class="amount-ksh">KSH ${subscription.amount.toLocaleString()}</span>
                </div>
            </td>
            <td>
                <div class="action-buttons">
                    ${subscription.status === 'active' ?
                        '<button class="btn-action extend">Extend</button><button class="btn-action cancel">Cancel</button>' :
                        '<button class="btn-action renew">Renew</button><button class="btn-action upgrade">Upgrade</button>'
                    }
                </div>
            </td>
        </tr>
    `).join('');
}

// Old loadTransactions function removed - now using real API data in loadDashboardData()

// Old renderTransactions function removed - now using updateRecentTransactions() with real API data

// Old updateStats function removed - now using updateDashboardStats() with real API data

function updateExchangeRate() {
    // Simulate API call to get exchange rate
    const rates = [129.14, 129.25, 128.98, 129.07, 129.33];
    const newRate = rates[Math.floor(Math.random() * rates.length)];
    
    const rateValue = document.querySelector('.rate-value');
    if (rateValue) {
        rateValue.textContent = `1 USD = ${newRate} KSH`;
    }
}

function handleTransactionAction(transactionId, action) {
    // Show confirmation
    const confirmed = confirm(`Are you sure you want to ${action} transaction ${transactionId}?`);
    
    if (confirmed) {
        // In a real app, this would make an API call
        console.log(`${action} transaction ${transactionId}`);
        
        // Update UI
        const row = document.querySelector(`[data-transaction-id="${transactionId}"]`);
        if (row) {
            const statusBadge = row.querySelector('.status-badge');
            if (statusBadge) {
                statusBadge.textContent = action === 'approve' ? 'completed' : 'rejected';
                statusBadge.className = `status-badge ${action === 'approve' ? 'completed' : 'failed'}`;
            }
        }
        
        // Show success message
        showNotification(`Transaction ${transactionId} has been ${action}d successfully!`, 'success');
    }
}

function filterTransactions(searchTerm) {
    const rows = document.querySelectorAll('.transactions-table tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const matches = text.includes(searchTerm.toLowerCase());
        row.style.display = matches ? '' : 'none';
    });
}

function filterTransactionsByStatus(status) {
    const rows = document.querySelectorAll('.transactions-table tbody tr');
    
    rows.forEach(row => {
        if (status === 'All Status') {
            row.style.display = '';
        } else {
            const statusBadge = row.querySelector('.status-badge');
            const matches = statusBadge.textContent.toLowerCase() === status.toLowerCase();
            row.style.display = matches ? '' : 'none';
        }
    });
}

function handlePagination(page) {
    // Update active pagination button
    document.querySelectorAll('.pagination-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const clickedBtn = Array.from(document.querySelectorAll('.pagination-btn'))
        .find(btn => btn.textContent === page);
    
    if (clickedBtn && !isNaN(page)) {
        clickedBtn.classList.add('active');
        // Load page data
        console.log(`Loading page ${page}`);
    }
}

async function handleLogout() {
    const confirmed = confirm('Are you sure you want to logout?');

    if (confirmed) {
        try {
            // Call logout API
            const response = await fetch(`${API_BASE_URL}?action=logout`, {
                method: 'POST',
                credentials: 'same-origin'
            });

            const data = await response.json();

            // Clear local storage regardless of API response
            localStorage.removeItem('adminInfo');

            // Redirect to login
            window.location.href = 'index.html';

        } catch (error) {
            console.error('Logout error:', error);
            // Clear local storage and redirect anyway
            localStorage.removeItem('adminInfo');
            window.location.href = 'index.html';
        }
    }
}

// Subscription Modal Functions
function openSubscriptionModal() {
    const modal = document.getElementById('subscriptionModal');
    const startDateInput = document.getElementById('startDate');

    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';

        // Set default start date to today
        if (startDateInput) {
            const today = new Date().toISOString().split('T')[0];
            startDateInput.value = today;
        }

        // Reset form
        const form = document.getElementById('subscriptionForm');
        if (form) {
            form.reset();
        }
    }
}

function closeSubscriptionModalHandler() {
    const modal = document.getElementById('subscriptionModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

function calculateAmount() {
    const planSelect = document.getElementById('planSelect');
    const durationSelect = document.getElementById('duration');
    const amountInput = document.getElementById('amount');

    if (planSelect && durationSelect && amountInput) {
        const selectedOption = planSelect.options[planSelect.selectedIndex];
        const monthlyPrice = selectedOption.getAttribute('data-price');
        const duration = parseInt(durationSelect.value);

        if (monthlyPrice && duration) {
            const totalAmount = parseInt(monthlyPrice) * duration;
            amountInput.value = totalAmount;
        }
    }
}

function handleSubscriptionSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const subscriptionData = {
        userId: formData.get('userSelect') || document.getElementById('userSelect').value,
        plan: formData.get('planSelect') || document.getElementById('planSelect').value,
        startDate: formData.get('startDate') || document.getElementById('startDate').value,
        duration: formData.get('duration') || document.getElementById('duration').value,
        paymentMethod: formData.get('paymentMethod') || document.getElementById('paymentMethod').value,
        amount: formData.get('amount') || document.getElementById('amount').value,
        autoRenew: document.getElementById('autoRenew').checked
    };

    // Validate required fields
    if (!subscriptionData.userId || !subscriptionData.plan || !subscriptionData.startDate) {
        showNotification('Please fill in all required fields.', 'error');
        return;
    }

    // Simulate API call
    setTimeout(() => {
        // In a real app, this would make an API call
        console.log('Creating subscription:', subscriptionData);

        showNotification('Subscription created successfully!', 'success');
        closeSubscriptionModalHandler();

        // Refresh subscriptions table
        if (document.getElementById('subscriptionsPage').style.display !== 'none') {
            loadSubscriptions();
        }
    }, 1000);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem 1.5rem',
        borderRadius: '0.5rem',
        color: '#fff',
        fontWeight: '600',
        zIndex: '9999',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease'
    });

    // Set background color based on type
    switch(type) {
        case 'success':
            notification.style.background = '#22c55e';
            break;
        case 'error':
            notification.style.background = '#ef4444';
            break;
        case 'warning':
            notification.style.background = '#f59e0b';
            break;
        default:
            notification.style.background = '#3b82f6';
    }

    // Add to DOM
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Package Management Modal Functions
function showCreatePackageModal() {
    document.getElementById('packageModalTitle').textContent = 'Create Package';
    document.getElementById('packageSubmitBtn').textContent = 'Create Package';
    document.getElementById('packageForm').reset();
    document.getElementById('packageId').value = '';
    document.getElementById('packageModal').style.display = 'flex';
}

function editPackage(packageId) {
    // Find package data and populate form
    document.getElementById('packageModalTitle').textContent = 'Edit Package';
    document.getElementById('packageSubmitBtn').textContent = 'Update Package';
    document.getElementById('packageId').value = packageId;
    document.getElementById('packageModal').style.display = 'flex';

    // TODO: Load package data and populate form
    console.log('Editing package:', packageId);
}

function closePackageModal() {
    document.getElementById('packageModal').style.display = 'none';
}

// Package form submission handler
function initializePackageForm() {
    const packageForm = document.getElementById('packageForm');
    if (packageForm) {
        packageForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const packageId = document.getElementById('packageId').value;
            const isEdit = packageId !== '';

            // Get form data
            const features = document.getElementById('packageFeatures').value
                .split('\n')
                .filter(f => f.trim())
                .map(f => f.trim());

            const packageData = {
                name: document.getElementById('packageName').value,
                plan_type: document.getElementById('packageType').value,
                description: document.getElementById('packageDescription').value,
                price_monthly: parseFloat(document.getElementById('monthlyPrice').value),
                price_yearly: parseFloat(document.getElementById('yearlyPrice').value),
                features: features,
                course_access_level: parseInt(document.getElementById('courseAccessLevel').value),
                max_devices: parseInt(document.getElementById('maxDevices').value),
                support_level: document.getElementById('supportLevel').value,
                telegram_access: document.getElementById('telegramAccess').checked,
                signal_access: document.getElementById('signalAccess').checked,
                is_active: document.getElementById('isActive').checked,
                currency: 'KES'
            };

            if (isEdit) {
                await updatePackage(packageId, packageData);
            } else {
                await createPackage(packageData);
            }
        });
    }
}

// Initialize package form when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePackageForm();
});
