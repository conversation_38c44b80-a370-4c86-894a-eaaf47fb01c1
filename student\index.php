<?php
/**
 * Student Portal Entry Point
 * 
 * Handles student authentication and dashboard access
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

session_start();

// Check if student is already logged in
if (isset($_SESSION['user_id']) && $_SESSION['user_role'] === 'user') {
    header('Location: dashboard.php');
    exit();
}

// If coming from login, redirect to dashboard
if (isset($_GET['redirect']) && $_GET['redirect'] === 'dashboard') {
    header('Location: dashboard.php');
    exit();
}

$page_title = "Student Portal - ForexClass";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <h2>ForexClass</h2>
                    <span>Student Portal</span>
                </div>
            </div>
            
            <div class="login-form">
                <h3>Welcome Back</h3>
                <p>Sign in to access your courses and trading resources</p>
                
                <form id="studentLoginForm">
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <div class="input-group">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="email" name="email" placeholder="Enter your email" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" name="password" placeholder="Enter your password" required>
                        </div>
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="remember" name="remember">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                        <a href="#forgot-password" class="forgot-link">Forgot Password?</a>
                    </div>
                    
                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In
                    </button>
                </form>
                
                <div class="login-footer">
                    <p>Don't have an account? <a href="../register">Sign up here</a></p>
                    <p><a href="../">Back to Website</a></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Alert Modal -->
    <div class="alert-modal" id="alertModal" style="display: none;">
        <div class="alert-content">
            <div class="alert-header">
                <span class="alert-icon" id="alertIcon"></span>
                <span class="alert-title" id="alertTitle"></span>
            </div>
            <div class="alert-message" id="alertMessage"></div>
            <button class="alert-close" onclick="closeAlert()">OK</button>
        </div>
    </div>
    
    <script src="login.js"></script>
</body>
</html>
