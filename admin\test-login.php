<?php
/**
 * Test Admin Login
 * 
 * Simple test to verify admin credentials work
 */

require_once '../api/config.php';

echo "<h1>Admin Login Test</h1>";

try {
    $pdo = getDBConnection();
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT id, name, email, role, status, email_verified FROM users WHERE email = ? AND role = 'admin'");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p>✅ Admin user found in database:</p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $admin['id'] . "</li>";
        echo "<li><strong>Name:</strong> " . htmlspecialchars($admin['name']) . "</li>";
        echo "<li><strong>Email:</strong> " . htmlspecialchars($admin['email']) . "</li>";
        echo "<li><strong>Role:</strong> " . htmlspecialchars($admin['role']) . "</li>";
        echo "<li><strong>Status:</strong> " . htmlspecialchars($admin['status']) . "</li>";
        echo "<li><strong>Email Verified:</strong> " . ($admin['email_verified'] ? 'Yes' : 'No') . "</li>";
        echo "</ul>";
        
        // Test password verification
        $testPassword = 'admin@123';
        $stmt = $pdo->prepare("SELECT password FROM users WHERE email = ? AND role = 'admin'");
        $stmt->execute(['<EMAIL>']);
        $result = $stmt->fetch();
        
        if ($result && password_verify($testPassword, $result['password'])) {
            echo "<p>✅ Password verification successful</p>";
        } else {
            echo "<p>❌ Password verification failed</p>";
        }
        
    } else {
        echo "<p>❌ Admin user not found</p>";
        echo "<p>Creating admin user...</p>";
        
        // Create admin user
        $hashedPassword = password_hash('admin@123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (name, email, phone, password, role, email_verified, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
                password = VALUES(password),
                role = VALUES(role),
                email_verified = VALUES(email_verified),
                status = VALUES(status)
        ");
        $stmt->execute([
            'Admin User',
            '<EMAIL>',
            '254700000000',
            $hashedPassword,
            'admin',
            1,
            'active'
        ]);
        
        echo "<p>✅ Admin user created/updated</p>";
    }
    
    echo "<h2>Login Credentials</h2>";
    echo "<div style='background: #f0f9ff; border: 1px solid #0ea5e9; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<p><strong>Email:</strong> <EMAIL></p>";
    echo "<p><strong>Password:</strong> admin@123</p>";
    echo "</div>";
    
    echo "<h2>Test Login</h2>";
    echo "<form method='post' style='max-width: 400px; margin: 20px 0;'>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label>Email:</label><br>";
    echo "<input type='email' name='test_email' value='<EMAIL>' style='width: 100%; padding: 8px; margin-top: 5px;'>";
    echo "</div>";
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label>Password:</label><br>";
    echo "<input type='password' name='test_password' value='admin@123' style='width: 100%; padding: 8px; margin-top: 5px;'>";
    echo "</div>";
    echo "<button type='submit' name='test_login' style='background: #4f46e5; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test Login</button>";
    echo "</form>";
    
    // Handle test login
    if (isset($_POST['test_login'])) {
        $email = $_POST['test_email'];
        $password = $_POST['test_password'];
        
        $stmt = $pdo->prepare("SELECT id, name, email, password, role FROM users WHERE email = ? AND role = 'admin' AND status = 'active'");
        $stmt->execute([$email]);
        $admin = $stmt->fetch();
        
        if ($admin && password_verify($password, $admin['password'])) {
            echo "<div style='background: #dcfce7; border: 1px solid #10b981; padding: 15px; border-radius: 5px; margin: 15px 0; color: #166534;'>";
            echo "<h3>✅ Login Test Successful!</h3>";
            echo "<p>Admin authentication is working correctly.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #fee2e2; border: 1px solid #ef4444; padding: 15px; border-radius: 5px; margin: 15px 0; color: #991b1b;'>";
            echo "<h3>❌ Login Test Failed!</h3>";
            echo "<p>Check your credentials or database connection.</p>";
            echo "</div>";
        }
    }
    
    echo "<h2>Quick Links</h2>";
    echo "<ul>";
    echo "<li><a href='index.php'>Admin Login Page</a></li>";
    echo "<li><a href='dashboard.php'>Admin Dashboard</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2 {
    color: #333;
}
a {
    color: #4f46e5;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
