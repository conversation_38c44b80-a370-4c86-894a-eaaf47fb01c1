<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'forex_class');
define('DB_USER', 'root');
define('DB_PASS', '');

// Application configuration
define('APP_NAME', 'ForexClass');
define('APP_URL', 'http://localhost/Forex');
define('APP_EMAIL', '<EMAIL>');

// Security configuration
define('JWT_SECRET', 'your-secret-key-here-change-this-in-production');
define('SESSION_LIFETIME', 24 * 60 * 60); // 24 hours in seconds
define('REMEMBER_LIFETIME', 30 * 24 * 60 * 60); // 30 days in seconds

// Email configuration (for future use)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');

// Function to get database connection
function getDBConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection error: " . $e->getMessage());
        throw new Exception("Database connection failed");
    }
}

// Function to validate email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Function to validate phone number (Kenyan format)
function validatePhone($phone) {
    return preg_match('/^254[0-9]{9}$/', $phone);
}

// Function to generate secure token
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// Function to send JSON response
function sendJsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Function to log errors
function logError($message, $context = []) {
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if (!empty($context)) {
        $log_message .= " - Context: " . json_encode($context);
    }
    error_log($log_message);
}
?>
