// Student Telegram JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeTelegram();
});

function initializeTelegram() {
    loadTelegramData();
    checkUserSubscription();
    initializeLogout();
    startActivityRefresh();
}

function loadTelegramData() {
    loadCommunityStats();
    loadRecentActivity();
}

function loadCommunityStats() {
    // Mock data - replace with actual API call
    const stats = {
        members: 2847,
        messagesToday: 156,
        signalsShared: 12,
        rating: 4.9
    };
    
    // Update stats display (already in HTML)
    console.log('Community stats loaded:', stats);
}

async function checkUserSubscription() {
    try {
        const userIdMeta = document.querySelector('meta[name="user-id"]');
        const userId = userIdMeta ? userIdMeta.content : null;
        
        if (!userId) return;
        
        const response = await fetch('../api/subscription.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_current_subscription',
                user_id: userId
            })
        });
        
        const data = await response.json();
        
        if (data.success && data.subscription) {
            const subscription = data.subscription;
            updateChannelAccess(subscription);
        } else {
            // User has no active subscription - free access only
            updateChannelAccess(null);
        }
        
    } catch (error) {
        console.error('Error checking subscription:', error);
        updateChannelAccess(null);
    }
}

function updateChannelAccess(subscription) {
    const signalsBtn = document.getElementById('signalsChannelBtn');
    const vipBtn = document.getElementById('vipChannelBtn');
    
    if (subscription) {
        // Check subscription level and update buttons accordingly
        if (subscription.telegram_access) {
            if (subscription.plan_type === 'premium' || subscription.plan_type === 'vip') {
                // Premium or VIP - can access signals channel
                signalsBtn.innerHTML = '<i class="fab fa-telegram"></i> Join Channel';
                signalsBtn.className = 'btn btn-primary btn-telegram';
                signalsBtn.onclick = () => joinChannel('signals');
            }
            
            if (subscription.plan_type === 'vip') {
                // VIP - can access VIP channel
                vipBtn.innerHTML = '<i class="fab fa-telegram"></i> Join VIP Channel';
                vipBtn.className = 'btn btn-primary btn-telegram';
                vipBtn.onclick = () => joinChannel('vip');
            }
        }
    }
    
    // Free users keep the default locked buttons
}

function joinChannel(channelType) {
    const channels = {
        main: {
            name: 'ForexClass Main',
            url: 'https://t.me/forexclass_main',
            description: 'Main community channel'
        },
        signals: {
            name: 'Premium Signals',
            url: 'https://t.me/forexclass_signals',
            description: 'Premium trading signals channel'
        },
        vip: {
            name: 'VIP Traders',
            url: 'https://t.me/forexclass_vip',
            description: 'Exclusive VIP channel'
        }
    };
    
    const channel = channels[channelType];
    
    if (channel) {
        // Show confirmation modal
        if (confirm(`Join ${channel.name}?\n\n${channel.description}\n\nThis will open Telegram.`)) {
            // Open Telegram channel
            window.open(channel.url, '_blank');
            
            // Track channel join
            trackChannelJoin(channelType);
        }
    }
}

function joinPremiumChannel(channelType) {
    // Show upgrade modal for premium channels
    showUpgradeModal(channelType);
}

function joinVipChannel(channelType) {
    // Show upgrade modal for VIP channels
    showUpgradeModal(channelType, 'vip');
}

function showUpgradeModal(channelType, requiredPlan = 'premium') {
    const planName = requiredPlan === 'vip' ? 'VIP' : 'Premium';
    const message = `This channel requires a ${planName} subscription.\n\nWould you like to upgrade your plan?`;
    
    if (confirm(message)) {
        // Redirect to subscription page
        window.location.href = 'subscription.php';
    }
}

function startBot() {
    // Open ForexClass bot
    const botUrl = 'https://t.me/ForexClassBot';
    
    if (confirm('Start ForexClass Bot?\n\nThis will open Telegram and start a conversation with our trading assistant bot.')) {
        window.open(botUrl, '_blank');
        
        // Track bot start
        trackBotStart();
    }
}

function loadRecentActivity() {
    // Mock data - replace with actual API call
    const activities = [
        {
            type: 'signal',
            icon: 'fas fa-chart-line',
            title: 'New Signal Posted',
            description: 'EUR/USD BUY signal shared in Premium Signals channel',
            time: '5 minutes ago'
        },
        {
            type: 'members',
            icon: 'fas fa-users',
            title: 'New Members Joined',
            description: '15 new traders joined the main community today',
            time: '2 hours ago'
        },
        {
            type: 'education',
            icon: 'fas fa-graduation-cap',
            title: 'Educational Content',
            description: 'New trading tutorial shared in the main channel',
            time: '4 hours ago'
        },
        {
            type: 'analysis',
            icon: 'fas fa-chart-area',
            title: 'Market Analysis',
            description: 'Weekly market outlook posted by our analysts',
            time: '6 hours ago'
        },
        {
            type: 'community',
            icon: 'fas fa-comments',
            title: 'Community Discussion',
            description: 'Active discussion about USD strength in main channel',
            time: '8 hours ago'
        }
    ];
    
    const activityFeed = document.querySelector('.activity-feed');
    
    let activityHTML = '';
    
    activities.forEach(activity => {
        activityHTML += `
            <div class="activity-item">
                <div class="activity-avatar">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <h4>${activity.title}</h4>
                    <p>${activity.description}</p>
                    <span class="activity-time">${activity.time}</span>
                </div>
            </div>
        `;
    });
    
    activityFeed.innerHTML = activityHTML;
}

function refreshActivity() {
    const refreshBtn = document.querySelector('.telegram-activity .btn');
    const originalText = refreshBtn.innerHTML;
    
    // Show loading state
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    refreshBtn.disabled = true;
    
    // Reload activity data
    setTimeout(() => {
        loadRecentActivity();
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;
        
        showNotification('Activity refreshed!', 'success');
    }, 1000);
}

function trackChannelJoin(channelType) {
    // Track channel join for analytics
    console.log(`User joined ${channelType} channel`);
    
    // Send tracking data to API
    fetch('../api/telegram.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'track_channel_join',
            channel_type: channelType,
            user_id: document.querySelector('meta[name="user-id"]')?.content
        })
    }).catch(error => {
        console.error('Error tracking channel join:', error);
    });
}

function trackBotStart() {
    // Track bot start for analytics
    console.log('User started ForexClass bot');
    
    // Send tracking data to API
    fetch('../api/telegram.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'track_bot_start',
            user_id: document.querySelector('meta[name="user-id"]')?.content
        })
    }).catch(error => {
        console.error('Error tracking bot start:', error);
    });
}

function startActivityRefresh() {
    // Auto-refresh activity every 5 minutes
    setInterval(() => {
        loadRecentActivity();
    }, 300000); // 5 minutes
}

function showNotification(message, type = 'info') {
    // Create and show notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#4f46e5'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function initializeLogout() {
    const logoutBtn = document.getElementById('logoutBtn');
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (confirm('Are you sure you want to logout?')) {
                // Clear user data
                localStorage.removeItem('userInfo');
                localStorage.removeItem('userToken');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('authToken');
                
                // Call logout API to destroy session
                fetch('../api/logout.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(() => {
                    // Redirect to home page
                    window.location.href = '../';
                }).catch(() => {
                    // Redirect anyway
                    window.location.href = '../';
                });
            }
        });
    }
}

// Add CSS for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .btn-telegram {
        background: linear-gradient(135deg, #0088cc, #229ed9);
        color: white;
        border: none;
    }
    
    .btn-telegram:hover {
        background: linear-gradient(135deg, #006699, #1a7db8);
        transform: translateY(-1px);
    }
    
    .btn-premium {
        background: linear-gradient(135deg, #4f46e5, #7c3aed);
        color: white;
        border: none;
        cursor: pointer;
    }
    
    .btn-premium:hover {
        background: linear-gradient(135deg, #3730a3, #6d28d9);
        transform: translateY(-1px);
    }
    
    .btn-vip {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
        border: none;
        cursor: pointer;
    }
    
    .btn-vip:hover {
        background: linear-gradient(135deg, #d97706, #b45309);
        transform: translateY(-1px);
    }
`;
document.head.appendChild(style);
