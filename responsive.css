/* Complete Website Responsiveness */

/* Features Section Responsiveness */
.why-choose {
    padding: 80px 0;
    background: #f8fafc;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: #fff;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: #fff;
    font-size: 1.5rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
    font-weight: 600;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
    font-size: 0.95rem;
}

@media (max-width: 1200px) {
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .why-choose {
        padding: 60px 0;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.2rem;
    }
    
    .feature-card h3 {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .why-choose {
        padding: 40px 0;
    }
    
    .feature-card {
        padding: 1.2rem;
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1rem;
        margin-bottom: 1rem;
    }
    
    .feature-card h3 {
        font-size: 1.1rem;
        margin-bottom: 0.8rem;
    }
    
    .feature-card p {
        font-size: 0.9rem;
    }
}

/* Courses Section Responsiveness */
.courses {
    padding: 80px 0;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.course-card {
    background: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.course-card:hover {
    transform: translateY(-5px);
}

.course-card.featured {
    border: 2px solid #4f46e5;
    transform: scale(1.02);
}

.course-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-card:hover .course-image img {
    transform: scale(1.05);
}

.course-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #4f46e5;
    color: #fff;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.course-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.course-content h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
    font-weight: 600;
}

.course-content p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
    flex: 1;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    color: #666;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.course-meta span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.course-price {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.original-price {
    text-decoration: line-through;
    color: #999;
    font-size: 0.9rem;
}

.current-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #4f46e5;
}

@media (max-width: 1200px) {
    .courses-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .courses {
        padding: 60px 0;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .course-card.featured {
        transform: none;
    }
    
    .course-image {
        height: 180px;
    }
    
    .course-content {
        padding: 1.2rem;
    }
    
    .course-meta {
        flex-direction: column;
        gap: 0.3rem;
    }
    
    .course-meta span {
        justify-content: flex-start;
    }
}

@media (max-width: 480px) {
    .courses {
        padding: 40px 0;
    }
    
    .course-image {
        height: 160px;
    }
    
    .course-content {
        padding: 1rem;
    }
    
    .course-content h3 {
        font-size: 1.2rem;
        margin-bottom: 0.8rem;
    }
    
    .course-content p {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    
    .course-price {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .current-price {
        font-size: 1.3rem;
    }
}

/* Pricing Section Responsiveness */
.pricing {
    padding: 80px 0;
    background: #f8fafc;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: #fff;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.pricing-card:hover {
    transform: translateY(-5px);
}

.pricing-card.popular {
    border: 2px solid #4f46e5;
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #4f46e5;
    color: #fff;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.plan-header h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
    font-weight: 600;
}

.plan-price {
    margin-bottom: 2rem;
}

.plan-price .currency {
    font-size: 1rem;
    color: #666;
    font-weight: 600;
}

.plan-price .amount {
    font-size: 3rem;
    font-weight: 800;
    color: #4f46e5;
    margin: 0 4px;
}

.plan-price .period {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
}

.plan-features {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
    flex: 1;
    padding: 0;
}

.plan-features li {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
}

.plan-features .fa-check {
    color: #22c55e;
    font-size: 0.9rem;
}

.plan-features .fa-times {
    color: #ef4444;
    font-size: 0.9rem;
}

@media (max-width: 1200px) {
    .pricing-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .pricing {
        padding: 60px 0;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        max-width: 400px;
    }

    .pricing-card.popular {
        transform: none;
    }

    .pricing-card {
        padding: 1.5rem;
    }

    .plan-price .amount {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .pricing {
        padding: 40px 0;
    }

    .pricing-card {
        padding: 1.2rem;
    }

    .plan-header h3 {
        font-size: 1.3rem;
    }

    .plan-price .amount {
        font-size: 2.2rem;
    }

    .plan-features li {
        font-size: 0.9rem;
        padding: 0.4rem 0;
    }
}

/* Contact Section Responsiveness */
.contact {
    padding: 80px 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-item i {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-item h4 {
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 600;
}

.contact-item p {
    color: #666;
    margin: 0;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.contact-form input,
.contact-form textarea {
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #4f46e5;
}

.contact-form textarea {
    resize: vertical;
    min-height: 120px;
}

@media (max-width: 768px) {
    .contact {
        padding: 60px 0;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-info {
        gap: 1.5rem;
    }

    .contact-item {
        gap: 0.8rem;
    }

    .contact-item i {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .contact {
        padding: 40px 0;
    }

    .contact-content {
        gap: 2rem;
    }

    .contact-info {
        gap: 1.2rem;
    }

    .contact-item {
        gap: 0.6rem;
    }

    .contact-item i {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .contact-form input,
    .contact-form textarea {
        padding: 10px 14px;
        font-size: 14px;
    }
}

/* Footer Responsiveness */
.footer {
    background: #1a1a1a;
    color: #fff;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #4f46e5;
    font-weight: 600;
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.95rem;
}

.footer-section ul li a:hover {
    color: #4f46e5;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #333;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #4f46e5;
    transform: translateY(-2px);
}

.newsletter {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.newsletter input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #333;
    border-radius: 5px;
    background: #333;
    color: #fff;
    font-size: 14px;
}

.newsletter input::placeholder {
    color: #ccc;
}

.newsletter button {
    padding: 10px 20px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: #fff;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.newsletter button:hover {
    transform: translateY(-1px);
}

.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 2rem;
    text-align: center;
    color: #ccc;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .footer {
        padding: 40px 0 20px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }

    .newsletter {
        flex-direction: column;
        max-width: 300px;
        margin: 1rem auto 0;
    }

    .newsletter input {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 480px) {
    .footer {
        padding: 30px 0 15px;
    }

    .footer-content {
        gap: 1.2rem;
    }

    .footer-section h3,
    .footer-section h4 {
        font-size: 1.1rem;
        margin-bottom: 0.8rem;
    }

    .footer-section p,
    .footer-section ul li a {
        font-size: 0.9rem;
    }

    .social-links a {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .newsletter input,
    .newsletter button {
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* Modal Responsiveness */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    animation: 0.3s modalSlideIn;
    max-height: 90vh;
    overflow-y: auto;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    background: #fff;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: 700;
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 10% auto;
        max-height: 85vh;
    }

    .modal-header {
        padding: 1.2rem;
    }

    .modal-header h3 {
        font-size: 1.2rem;
    }

    .modal-body {
        padding: 1.2rem;
    }

    .close {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 98%;
        margin: 5% auto;
        max-height: 90vh;
        border-radius: 10px;
    }

    .modal-header {
        padding: 1rem;
        border-radius: 10px 10px 0 0;
    }

    .modal-header h3 {
        font-size: 1.1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .close {
        font-size: 22px;
    }
}

/* Button Responsiveness */
.btn-primary,
.btn-secondary,
.btn-outline {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    font-size: 14px;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: #fff;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #4f46e5;
    border: 2px solid #4f46e5;
}

.btn-secondary:hover {
    background: #4f46e5;
    color: #fff;
}

.btn-outline {
    background: transparent;
    color: #333;
    border: 2px solid #e1e5e9;
}

.btn-outline:hover {
    border-color: #4f46e5;
    color: #4f46e5;
}

.btn-primary.full-width,
.btn-secondary.full-width,
.btn-outline.full-width {
    width: 100%;
}

@media (max-width: 480px) {
    .btn-primary,
    .btn-secondary,
    .btn-outline {
        padding: 10px 20px;
        font-size: 13px;
    }
}

/* Responsive Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.m-0 { margin: 0; }
.p-0 { padding: 0; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.pt-1 { padding-top: 0.5rem; }
.pt-2 { padding-top: 1rem; }
.pt-3 { padding-top: 1.5rem; }
.pt-4 { padding-top: 2rem; }

.pb-1 { padding-bottom: 0.5rem; }
.pb-2 { padding-bottom: 1rem; }
.pb-3 { padding-bottom: 1.5rem; }
.pb-4 { padding-bottom: 2rem; }

/* Mobile-specific utilities */
@media (max-width: 768px) {
    .d-md-none { display: none; }
    .d-md-block { display: block; }
    .d-md-flex { display: flex; }

    .text-md-center { text-align: center; }
    .text-md-left { text-align: left; }

    .w-md-100 { width: 100%; }

    .mt-md-0 { margin-top: 0; }
    .mt-md-1 { margin-top: 0.5rem; }
    .mt-md-2 { margin-top: 1rem; }

    .mb-md-0 { margin-bottom: 0; }
    .mb-md-1 { margin-bottom: 0.5rem; }
    .mb-md-2 { margin-bottom: 1rem; }
}

@media (max-width: 480px) {
    .d-sm-none { display: none; }
    .d-sm-block { display: block; }
    .d-sm-flex { display: flex; }

    .text-sm-center { text-align: center; }
    .text-sm-left { text-align: left; }

    .w-sm-100 { width: 100%; }

    .mt-sm-0 { margin-top: 0; }
    .mt-sm-1 { margin-top: 0.5rem; }

    .mb-sm-0 { margin-bottom: 0; }
    .mb-sm-1 { margin-bottom: 0.5rem; }
}

/* Prevent body scroll when mobile nav is open */
body.nav-open {
    overflow: hidden;
}

/* Smooth scrolling for all browsers */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-primary {
        background: #000;
        color: #fff;
        border: 2px solid #000;
    }

    .btn-secondary {
        background: #fff;
        color: #000;
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}

/* Print styles */
@media print {
    .navbar,
    .hamburger,
    .scroll-indicator,
    .floating-shapes,
    .performance-card {
        display: none !important;
    }

    .hero {
        background: #fff !important;
        color: #000 !important;
        padding: 20px 0 !important;
    }

    .hero-title,
    .gradient-text {
        color: #000 !important;
        background: none !important;
        -webkit-text-fill-color: #000 !important;
    }

    .dashboard-main {
        border: 1px solid #000 !important;
        background: #fff !important;
    }

    * {
        box-shadow: none !important;
        text-shadow: none !important;
    }
}
