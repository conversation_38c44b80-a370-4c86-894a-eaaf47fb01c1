# ForexClass Website Configuration
# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Enable browser caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Redirect to HTTPS (optional - uncomment if you have SSL)
# RewriteEngine On
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Clean URLs and Route Management
RewriteEngine On

# API routes (keep existing)
RewriteRule ^api/mpesa/stkpush$ api/mpesa.php [L]
RewriteRule ^api/mpesa/callback$ api/callback.php [L]

# Main website clean routes
RewriteRule ^pricing/?$ pricing.php [NC,L]
RewriteRule ^courses/?$ courses.php [NC,L]
RewriteRule ^about/?$ about.php [NC,L]
RewriteRule ^contact/?$ contact.php [NC,L]
RewriteRule ^login/?$ login.php [NC,L]
RewriteRule ^register/?$ register.php [NC,L]
RewriteRule ^dashboard/?$ dashboard.php [NC,L]

# Admin routes
RewriteRule ^admin/?$ admin/index.php [NC,L]
RewriteRule ^admin/login/?$ admin/index.php [NC,L]
RewriteRule ^admin/dashboard/?$ admin/dashboard.php [NC,L]

# User management routes
RewriteRule ^adduser/?$ admin/adduser.php [NC,L]
RewriteRule ^edituser/([0-9]+)/?$ admin/edituser.php?id=$1 [NC,L]
RewriteRule ^deleteuser/([0-9]+)/?$ admin/deleteuser.php?id=$1 [NC,L]

# Package management routes
RewriteRule ^addpackage/?$ admin/addpackage.php [NC,L]
RewriteRule ^editpackage/([0-9]+)/?$ admin/editpackage.php?id=$1 [NC,L]
RewriteRule ^deletepackage/([0-9]+)/?$ admin/deletepackage.php?id=$1 [NC,L]

# Student dashboard routes
RewriteRule ^student/?$ student/index.php [NC,L]
RewriteRule ^student/dashboard/?$ student/dashboard.php [NC,L]
RewriteRule ^student/profile/?$ student/profile.php [NC,L]
RewriteRule ^student/subscription/?$ student/subscription.php [NC,L]

# Remove .html and .php extensions from existing files
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME}.html -f
RewriteRule ^([^\.]+)$ $1.html [NC,L]
