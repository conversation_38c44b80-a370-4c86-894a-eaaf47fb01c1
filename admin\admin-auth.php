<?php
session_start();
require_once '../api/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

function adminLogin() {
    try {
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            sendJsonResponse(['success' => false, 'message' => 'Invalid JSON input'], 400);
        }
        
        $email = trim($input['email'] ?? '');
        $password = $input['password'] ?? '';
        $rememberMe = $input['rememberMe'] ?? false;
        
        // Validation
        if (empty($email) || empty($password)) {
            sendJsonResponse(['success' => false, 'message' => 'Email and password are required'], 400);
        }
        
        if (!validateEmail($email)) {
            sendJsonResponse(['success' => false, 'message' => 'Invalid email format'], 400);
        }
        
        // Get database connection
        $pdo = getDBConnection();
        
        // Check if admin exists and is active (using existing users table)
        $stmt = $pdo->prepare("SELECT id, name, email, password, role FROM users WHERE email = ? AND role = 'admin' AND status = 'active'");
        $stmt->execute([$email]);
        $admin = $stmt->fetch();

        if (!$admin) {
            sendJsonResponse(['success' => false, 'message' => 'Invalid admin credentials'], 401);
        }

        // Verify password
        if (!password_verify($password, $admin['password'])) {
            sendJsonResponse(['success' => false, 'message' => 'Invalid admin credentials'], 401);
        }

        // Update last login
        $updateStmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $updateStmt->execute([$admin['id']]);
        
        // Create session
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_email'] = $admin['email'];
        $_SESSION['admin_name'] = $admin['name'];
        $_SESSION['admin_role'] = $admin['role'];
        $_SESSION['admin_login_time'] = time();
        
        // Set session lifetime
        if ($rememberMe) {
            // 30 days
            ini_set('session.gc_maxlifetime', 30 * 24 * 60 * 60);
            session_set_cookie_params(30 * 24 * 60 * 60);
        } else {
            // 24 hours
            ini_set('session.gc_maxlifetime', 24 * 60 * 60);
            session_set_cookie_params(24 * 60 * 60);
        }
        
        // Generate session token for additional security
        $sessionToken = generateToken();
        $_SESSION['admin_token'] = $sessionToken;
        
        // Store session in database (using existing user_sessions table)
        $sessionLifetime = $rememberMe ? (30 * 24 * 60 * 60) : (24 * 60 * 60);
        $expiresAt = date('Y-m-d H:i:s', time() + $sessionLifetime);
        $sessionStmt = $pdo->prepare("INSERT INTO user_sessions (user_id, token, expires_at) VALUES (?, ?, ?)");
        $sessionStmt->execute([$admin['id'], $sessionToken, $expiresAt]);

        sendJsonResponse([
            'success' => true,
            'message' => 'Login successful',
            'admin' => [
                'id' => $admin['id'],
                'name' => $admin['name'],
                'email' => $admin['email'],
                'role' => $admin['role'],
                'token' => $sessionToken
            ]
        ]);
        
    } catch (Exception $e) {
        logError("Admin login error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'An error occurred during login'], 500);
    }
}

function checkAdminSession() {
    try {
        if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_token'])) {
            sendJsonResponse(['success' => false, 'message' => 'Not authenticated'], 401);
        }
        
        $pdo = getDBConnection();
        
        // Check if session token is valid (using existing user_sessions table)
        $stmt = $pdo->prepare("SELECT us.*, u.name, u.email, u.role FROM user_sessions us
                              JOIN users u ON us.user_id = u.id
                              WHERE us.user_id = ? AND us.token = ? AND us.expires_at > ? AND u.role = 'admin' AND u.status = 'active'");
        $stmt->execute([$_SESSION['admin_id'], $_SESSION['admin_token'], date('Y-m-d H:i:s')]);
        $session = $stmt->fetch();
        
        if (!$session) {
            // Clear invalid session
            session_destroy();
            sendJsonResponse(['success' => false, 'message' => 'Session expired'], 401);
        }
        
        sendJsonResponse([
            'success' => true,
            'admin' => [
                'id' => $session['user_id'],
                'name' => $session['name'],
                'email' => $session['email'],
                'role' => $session['role']
            ]
        ]);
        
    } catch (Exception $e) {
        logError("Admin session check error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Session check failed'], 500);
    }
}

function adminLogout() {
    try {
        if (isset($_SESSION['admin_id']) && isset($_SESSION['admin_token'])) {
            $pdo = getDBConnection();
            
            // Remove session from database (using existing user_sessions table)
            $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE user_id = ? AND token = ?");
            $stmt->execute([$_SESSION['admin_id'], $_SESSION['admin_token']]);
        }
        
        // Destroy session
        session_destroy();
        
        sendJsonResponse(['success' => true, 'message' => 'Logged out successfully']);
        
    } catch (Exception $e) {
        logError("Admin logout error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Logout failed'], 500);
    }
}

// Handle different request methods
$method = $_SERVER['REQUEST_METHOD'];

// Get action from URL parameters or JSON body
$action = $_GET['action'] ?? '';
if (empty($action) && $method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
}

switch ($method) {
    case 'POST':
        if ($action === 'login') {
            adminLogin();
        } elseif ($action === 'logout') {
            adminLogout();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action: ' . $action], 400);
        }
        break;
        
    case 'GET':
        if ($action === 'check') {
            checkAdminSession();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;
        
    default:
        sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
        break;
}
?>
