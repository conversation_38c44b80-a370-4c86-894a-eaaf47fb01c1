/* Authentication Modal Styles */
.auth-modal {
    max-width: 450px;
}

.auth-form {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 600;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s, box-shadow 0.3s;
    background: #fff;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input::placeholder {
    color: #999;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
    padding-left: 25px;
    margin-bottom: 0;
    color: #333;
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 18px;
    width: 18px;
    background-color: #fff;
    border: 2px solid #e1e5e9;
    border-radius: 3px;
    transition: all 0.3s;
}

.checkbox-container:hover input ~ .checkmark {
    border-color: #667eea;
}

.checkbox-container input:checked ~ .checkmark {
    background-color: #667eea;
    border-color: #667eea;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container .checkmark:after {
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.forgot-password,
.terms-link,
.privacy-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
}

.forgot-password:hover,
.terms-link:hover,
.privacy-link:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.auth-toggle {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
    margin-top: 1rem;
}

.auth-toggle p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.auth-toggle a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s;
}

.auth-toggle a:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.auth-loading {
    text-align: center;
    padding: 2rem 0;
}

.auth-loading .spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.auth-loading p {
    color: #666;
    margin: 0;
}

.auth-alert {
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    font-size: 0.9rem;
}

.auth-alert.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.auth-alert.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.auth-alert.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Button styles for auth forms */
.auth-form .btn-primary {
    padding: 14px 24px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s;
}

.auth-form .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.auth-form .btn-primary i {
    margin-right: 8px;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .auth-modal {
        max-width: 95%;
        margin: 5% auto;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .checkbox-container {
        font-size: 0.85rem;
    }
}

/* Animation for form switching */
.auth-form {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.auth-form.fade-out {
    opacity: 0;
    transform: translateX(-20px);
}

.auth-form.fade-in {
    opacity: 1;
    transform: translateX(0);
}
