<?php
/**
 * Admin Dashboard - Enhanced Version
 *
 * Complete admin dashboard for managing subscription plans, users, and platform settings
 *
 * <AUTHOR> Team
 * @version 2.0
 */

session_start();
require_once '../api/config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit();
}

$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'] ?? 'Admin';

// Get dashboard statistics
try {
    $pdo = getDBConnection();

    // Get total users
    $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users WHERE role = 'user'");
    $totalUsers = $stmt->fetch()['total_users'];

    // Get active subscriptions
    $stmt = $pdo->query("SELECT COUNT(*) as active_subs FROM user_subscriptions WHERE status = 'active' AND end_date > NOW()");
    $activeSubscriptions = $stmt->fetch()['active_subs'];

    // Get total revenue this month
    $stmt = $pdo->query("SELECT SUM(amount) as monthly_revenue FROM transactions WHERE status = 'completed' AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
    $monthlyRevenue = $stmt->fetch()['monthly_revenue'] ?? 0;

    // Get subscription plans
    $stmt = $pdo->query("SELECT * FROM subscription_plans ORDER BY price_monthly ASC");
    $subscriptionPlans = $stmt->fetchAll();

    // Get recent transactions
    $stmt = $pdo->prepare("
        SELECT t.*, u.name as user_name, u.email as user_email
        FROM transactions t
        JOIN users u ON t.user_id = u.id
        ORDER BY t.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recentTransactions = $stmt->fetchAll();

} catch (Exception $e) {
    $error = "Failed to load dashboard data: " . $e->getMessage();
}

$page_title = "Admin Dashboard - ForexClass";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <meta name="admin-id" content="<?php echo $admin_id; ?>">
    <meta name="admin-name" content="<?php echo htmlspecialchars($admin_name); ?>">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <span>ForexClass Admin</span>
                </div>
            </div>

            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="user-details">
                    <span class="welcome-text">Welcome back,</span>
                    <span class="user-name"><?php echo htmlspecialchars($admin_name); ?></span>
                </div>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="dashboard.php" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                            <span class="nav-badge">Overview</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="users.php" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                            <span class="nav-badge">Manage Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="subscriptions.php" class="nav-link">
                            <i class="fas fa-crown"></i>
                            <span>Subscriptions</span>
                            <span class="nav-badge">User Plans</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="plans.php" class="nav-link">
                            <i class="fas fa-box"></i>
                            <span>Subscription Plans</span>
                            <span class="nav-badge">Pricing Plans</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="transactions.php" class="nav-link">
                            <i class="fas fa-credit-card"></i>
                            <span>Transactions</span>
                            <span class="nav-badge">Payments</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="telegram.php" class="nav-link">
                            <i class="fab fa-telegram"></i>
                            <span>Telegram</span>
                            <span class="nav-badge">Channel Management</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                            <span class="nav-badge">Platform Settings</span>
                        </a>
                    </li>
                </ul>

                <div class="sidebar-footer">
                    <a href="../" class="nav-link">
                        <i class="fas fa-external-link-alt"></i>
                        <span>View Website</span>
                    </a>
                    <a href="#logout" class="nav-link logout" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="dashboard-header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="page-title">
                        <h1><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h1>
                        <p>Manage your ForexClass platform</p>
                    </div>
                </div>

                <div class="header-right">
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="window.location.href='plans.php'">
                            <i class="fas fa-plus"></i>
                            Manage Plans
                        </button>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-value"><?php echo number_format($totalUsers ?? 0); ?></h3>
                            <p>Total Users</p>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12%</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-value"><?php echo number_format($activeSubscriptions ?? 0); ?></h3>
                            <p>Active Subscriptions</p>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8%</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-value">KSH <?php echo number_format($monthlyRevenue ?? 0); ?></h3>
                            <p>Monthly Revenue</p>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>+15%</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-value"><?php echo count($subscriptionPlans ?? []); ?></h3>
                            <p>Subscription Plans</p>
                        </div>
                        <div class="stat-action">
                            <a href="plans.php" class="btn btn-sm">
                                <i class="fas fa-edit"></i>
                                Manage
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Subscription Plans Management -->
                <div class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-box"></i> Subscription Plans</h2>
                        <p>Manage pricing plans that sync with student dashboard and website</p>
                        <div class="section-actions">
                            <button class="btn btn-primary" onclick="showAddPlanModal()">
                                <i class="fas fa-plus"></i>
                                Add New Plan
                            </button>
                        </div>
                    </div>

                    <div class="plans-grid">
                        <?php if (!empty($subscriptionPlans)): ?>
                            <?php foreach ($subscriptionPlans as $plan): ?>
                                <div class="plan-card" data-plan-id="<?php echo $plan['id']; ?>">
                                    <div class="plan-header">
                                        <h3><?php echo htmlspecialchars($plan['name']); ?></h3>
                                        <div class="plan-status">
                                            <span class="status-badge <?php echo $plan['is_active'] ? 'active' : 'inactive'; ?>">
                                                <?php echo $plan['is_active'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="plan-pricing">
                                        <div class="price-item">
                                            <span class="price">KSH <?php echo number_format($plan['price_monthly']); ?></span>
                                            <span class="period">/month</span>
                                        </div>
                                        <div class="price-item">
                                            <span class="price">KSH <?php echo number_format($plan['price_yearly']); ?></span>
                                            <span class="period">/year</span>
                                        </div>
                                    </div>

                                    <div class="plan-features">
                                        <p><?php echo htmlspecialchars($plan['description']); ?></p>
                                        <div class="feature-list">
                                            <?php
                                            $features = json_decode($plan['features'], true);
                                            if ($features):
                                                foreach ($features as $feature):
                                            ?>
                                                <div class="feature-item">
                                                    <i class="fas fa-check"></i>
                                                    <span><?php echo htmlspecialchars($feature); ?></span>
                                                </div>
                                            <?php
                                                endforeach;
                                            endif;
                                            ?>
                                        </div>
                                    </div>

                                    <div class="plan-access">
                                        <div class="access-item">
                                            <span class="access-label">Telegram Access:</span>
                                            <span class="access-value <?php echo $plan['telegram_access'] ? 'enabled' : 'disabled'; ?>">
                                                <?php echo $plan['telegram_access'] ? 'Enabled' : 'Disabled'; ?>
                                            </span>
                                        </div>
                                        <div class="access-item">
                                            <span class="access-label">Signal Access:</span>
                                            <span class="access-value <?php echo $plan['signal_access'] ? 'enabled' : 'disabled'; ?>">
                                                <?php echo $plan['signal_access'] ? 'Enabled' : 'Disabled'; ?>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="plan-actions">
                                        <button class="btn btn-outline" onclick="editPlan(<?php echo $plan['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                            Edit
                                        </button>
                                        <button class="btn btn-danger" onclick="deletePlan(<?php echo $plan['id']; ?>)">
                                            <i class="fas fa-trash"></i>
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-box-open"></i>
                                <h3>No Subscription Plans</h3>
                                <p>Create your first subscription plan to get started</p>
                                <button class="btn btn-primary" onclick="showAddPlanModal()">
                                    <i class="fas fa-plus"></i>
                                    Add First Plan
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-credit-card"></i> Recent Transactions</h2>
                        <p>Latest payment transactions from students</p>
                        <div class="section-actions">
                            <a href="transactions.php" class="btn btn-outline">
                                <i class="fas fa-eye"></i>
                                View All
                            </a>
                        </div>
                    </div>

                    <div class="transactions-table">
                        <?php if (!empty($recentTransactions)): ?>
                            <div class="table-header">
                                <div class="table-row">
                                    <div class="table-cell">User</div>
                                    <div class="table-cell">Plan</div>
                                    <div class="table-cell">Amount</div>
                                    <div class="table-cell">Status</div>
                                    <div class="table-cell">Date</div>
                                </div>
                            </div>
                            <div class="table-body">
                                <?php foreach ($recentTransactions as $transaction): ?>
                                    <div class="table-row">
                                        <div class="table-cell">
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <div class="user-details">
                                                    <span class="user-name"><?php echo htmlspecialchars($transaction['user_name']); ?></span>
                                                    <span class="user-email"><?php echo htmlspecialchars($transaction['user_email']); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="table-cell">
                                            <span class="plan-name"><?php echo htmlspecialchars($transaction['plan_type']); ?></span>
                                        </div>
                                        <div class="table-cell">
                                            <span class="amount">KSH <?php echo number_format($transaction['amount']); ?></span>
                                        </div>
                                        <div class="table-cell">
                                            <span class="status-badge <?php echo $transaction['status']; ?>">
                                                <?php echo ucfirst($transaction['status']); ?>
                                            </span>
                                        </div>
                                        <div class="table-cell">
                                            <span class="date"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-receipt"></i>
                                <h3>No Transactions Yet</h3>
                                <p>Transactions will appear here when students make payments</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Add/Edit Plan Modal -->
    <div class="modal-overlay" id="planModal" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modalTitle">Add New Subscription Plan</h3>
                <button class="modal-close" onclick="closePlanModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="planForm">
                    <input type="hidden" id="planId" name="plan_id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="planName">Plan Name</label>
                            <input type="text" id="planName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="planType">Plan Type</label>
                            <select id="planType" name="plan_type" required>
                                <option value="basic">Basic</option>
                                <option value="premium">Premium</option>
                                <option value="vip">VIP</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="planDescription">Description</label>
                        <textarea id="planDescription" name="description" rows="3" required></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="monthlyPrice">Monthly Price (KSH)</label>
                            <input type="number" id="monthlyPrice" name="price_monthly" required>
                        </div>
                        <div class="form-group">
                            <label for="yearlyPrice">Yearly Price (KSH)</label>
                            <input type="number" id="yearlyPrice" name="price_yearly" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Features (one per line)</label>
                        <textarea id="planFeatures" name="features" rows="5" placeholder="Enter features, one per line"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="telegramAccess" name="telegram_access">
                                <span class="checkmark"></span>
                                Telegram Access
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="signalAccess" name="signal_access">
                                <span class="checkmark"></span>
                                Signal Access
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="isActive" name="is_active" checked>
                            <span class="checkmark"></span>
                            Active Plan
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closePlanModal()">
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Save Plan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Initialize admin dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdminDashboard();
        });

        function initializeAdminDashboard() {
            // Initialize sidebar toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    document.querySelector('.sidebar').classList.toggle('collapsed');
                });
            }

            // Initialize logout
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (confirm('Are you sure you want to logout?')) {
                        window.location.href = 'admin-auth.php?action=logout';
                    }
                });
            }

            // Initialize plan form
            const planForm = document.getElementById('planForm');
            if (planForm) {
                planForm.addEventListener('submit', handlePlanSubmit);
            }
        }

        // Plan Management Functions
        function showAddPlanModal() {
            document.getElementById('modalTitle').textContent = 'Add New Subscription Plan';
            document.getElementById('planForm').reset();
            document.getElementById('planId').value = '';
            document.getElementById('planModal').style.display = 'flex';
        }

        function closePlanModal() {
            document.getElementById('planModal').style.display = 'none';
        }

        function editPlan(planId) {
            // Fetch plan data and populate form
            fetch(`../api/admin.php?action=get_plan&id=${planId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const plan = data.plan;
                        document.getElementById('modalTitle').textContent = 'Edit Subscription Plan';
                        document.getElementById('planId').value = plan.id;
                        document.getElementById('planName').value = plan.name;
                        document.getElementById('planType').value = plan.plan_type;
                        document.getElementById('planDescription').value = plan.description;
                        document.getElementById('monthlyPrice').value = plan.price_monthly;
                        document.getElementById('yearlyPrice').value = plan.price_yearly;

                        // Handle features
                        const features = JSON.parse(plan.features || '[]');
                        document.getElementById('planFeatures').value = features.join('\n');

                        document.getElementById('telegramAccess').checked = plan.telegram_access == 1;
                        document.getElementById('signalAccess').checked = plan.signal_access == 1;
                        document.getElementById('isActive').checked = plan.is_active == 1;

                        document.getElementById('planModal').style.display = 'flex';
                    } else {
                        alert('Failed to load plan data: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to load plan data');
                });
        }

        function deletePlan(planId) {
            if (confirm('Are you sure you want to delete this subscription plan? This action cannot be undone.')) {
                fetch('../api/admin.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'delete_plan',
                        plan_id: planId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Plan deleted successfully');
                        window.location.reload();
                    } else {
                        alert('Failed to delete plan: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to delete plan');
                });
            }
        }

        function handlePlanSubmit(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const planData = {
                action: document.getElementById('planId').value ? 'update_plan' : 'create_plan',
                plan_id: formData.get('plan_id'),
                name: formData.get('name'),
                plan_type: formData.get('plan_type'),
                description: formData.get('description'),
                price_monthly: formData.get('price_monthly'),
                price_yearly: formData.get('price_yearly'),
                features: formData.get('features').split('\n').filter(f => f.trim()),
                telegram_access: formData.get('telegram_access') ? 1 : 0,
                signal_access: formData.get('signal_access') ? 1 : 0,
                is_active: formData.get('is_active') ? 1 : 0
            };

            fetch('../api/admin.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(planData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(planData.action === 'create_plan' ? 'Plan created successfully' : 'Plan updated successfully');
                    closePlanModal();
                    window.location.reload();
                } else {
                    alert('Failed to save plan: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to save plan');
            });
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                closePlanModal();
            }
        });
    </script>
</body>
</html>
