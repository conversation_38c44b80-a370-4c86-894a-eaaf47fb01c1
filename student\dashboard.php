<?php
/**
 * Student Dashboard
 * 
 * Main dashboard for student portal with course access and subscription management
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

session_start();
require_once '../api/config.php';

// Check if student is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'user') {
    header('Location: index.php');
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Student';
$user_email = $_SESSION['user_email'] ?? '';

$page_title = "Dashboard - ForexClass Student Portal";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="user-id" content="<?php echo $user_id; ?>">
    <meta name="user-name" content="<?php echo htmlspecialchars($user_name); ?>">
    <meta name="user-email" content="<?php echo htmlspecialchars($user_email); ?>">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <h2>ForexClass</h2>
                    <span>Student Portal</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item active">
                        <a href="dashboard.php" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="courses.php" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            <span>My Courses</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="subscription.php" class="nav-link">
                            <i class="fas fa-crown"></i>
                            <span>Subscription</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="profile.php" class="nav-link">
                            <i class="fas fa-user"></i>
                            <span>Profile</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="signals.php" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span>Trading Signals</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="telegram.php" class="nav-link">
                            <i class="fab fa-telegram"></i>
                            <span>Telegram</span>
                        </a>
                    </li>
                </ul>
                
                <div class="sidebar-footer">
                    <a href="../" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>Back to Website</span>
                    </a>
                    <a href="#logout" class="nav-link logout" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="dashboard-header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="page-title">
                        <h1 id="pageTitle">Dashboard</h1>
                        <p id="pageSubtitle">Welcome to your learning portal</p>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <span class="user-name" id="userName"><?php echo htmlspecialchars($user_name); ?></span>
                            <span class="user-role">Student</span>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <!-- Dashboard Overview Page -->
                <div class="page-content active" id="dashboardPage">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-book-open"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="totalCourses">0</h3>
                                <p>Enrolled Courses</p>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="completedCourses">0</h3>
                                <p>Completed</p>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="studyHours">0</h3>
                                <p>Study Hours</p>
                            </div>
                        </div>

                        <div class="stat-card clickable" onclick="window.location.href='subscription.php'">
                            <div class="stat-icon">
                                <i class="fas fa-crown"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="subscriptionStatus">
                                    <?php echo $currentSubscription ? htmlspecialchars($currentSubscription['plan_name']) : 'Free'; ?>
                                </h3>
                                <p>Plan Status</p>
                            </div>
                            <div class="stat-action">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <h3>Quick Actions</h3>
                        <div class="actions-grid">
                            <div class="action-card" onclick="window.location.href='subscription.php'">
                                <div class="action-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <div class="action-content">
                                    <h4>Upgrade Plan</h4>
                                    <p>Get premium features</p>
                                </div>
                            </div>

                            <div class="action-card" onclick="window.location.href='courses.php'">
                                <div class="action-icon">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div class="action-content">
                                    <h4>Continue Learning</h4>
                                    <p>Resume your courses</p>
                                </div>
                            </div>

                            <div class="action-card" onclick="window.location.href='signals.php'">
                                <div class="action-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="action-content">
                                    <h4>Trading Signals</h4>
                                    <p>View latest signals</p>
                                </div>
                            </div>

                            <div class="action-card" onclick="window.location.href='telegram.php'">
                                <div class="action-icon">
                                    <i class="fab fa-telegram"></i>
                                </div>
                                <div class="action-content">
                                    <h4>Join Community</h4>
                                    <p>Connect with traders</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Subscription Status -->
                    <div class="content-section" id="subscriptionWidget">
                        <div class="section-header">
                            <h3>Subscription Status</h3>
                            <button class="btn btn-primary" onclick="window.location.href='subscription.php'">
                                <i class="fas fa-crown"></i>
                                Upgrade Plan
                            </button>
                        </div>
                        <div class="subscription-status" id="subscriptionStatusWidget">
                            <div class="current-plan">
                                <div class="plan-info">
                                    <h4 id="currentPlanName">Free Plan</h4>
                                    <p id="currentPlanDescription">Basic access to forex education</p>
                                </div>
                                <div class="plan-features">
                                    <ul id="currentPlanFeatures">
                                        <li><i class="fas fa-check"></i> Basic courses access</li>
                                        <li><i class="fas fa-times"></i> Premium signals</li>
                                        <li><i class="fas fa-times"></i> Telegram community</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="upgrade-prompt" id="upgradePrompt">
                                <div class="upgrade-content">
                                    <h4>🚀 Unlock Premium Features</h4>
                                    <p>Get access to advanced trading signals, premium courses, and exclusive community.</p>
                                    <button class="btn btn-gradient" onclick="window.location.href='subscription.php'">
                                        <i class="fas fa-rocket"></i>
                                        Upgrade Now
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="content-section">
                        <div class="section-header">
                            <h3>Recent Activity</h3>
                        </div>
                        <div class="activity-list" id="activityList">
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div class="activity-content">
                                    <h4>Welcome to ForexClass!</h4>
                                    <p>Start your forex learning journey</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Other pages will be loaded dynamically -->
                <div class="page-content" id="coursesPage" style="display: none;">
                    <div class="content-section">
                        <div class="section-header">
                            <h3>My Courses</h3>
                            <a href="../courses" class="btn btn-primary">Browse More Courses</a>
                        </div>
                        <div class="courses-grid" id="coursesGrid">
                            <!-- Courses will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <div class="page-content" id="subscriptionPage" style="display: none;">
                    <div class="content-section">
                        <div class="section-header">
                            <h3>Subscription Management</h3>
                        </div>
                        <div class="subscription-content" id="subscriptionContent">
                            <!-- Subscription details will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <div class="page-content" id="profilePage" style="display: none;">
                    <div class="content-section">
                        <div class="section-header">
                            <h3>Profile Settings</h3>
                        </div>
                        <div class="profile-content" id="profileContent">
                            <!-- Profile form will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <div class="page-content" id="signalsPage" style="display: none;">
                    <div class="content-section">
                        <div class="section-header">
                            <h3>Trading Signals</h3>
                        </div>
                        <div class="signals-content" id="signalsContent">
                            <!-- Trading signals will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <div class="page-content" id="telegramPage" style="display: none;">
                    <div class="content-section">
                        <div class="section-header">
                            <h3>Telegram Integration</h3>
                        </div>
                        <div class="telegram-content">
                            <div class="telegram-info">
                                <h4>Join Our Trading Community</h4>
                                <p>Get real-time signals and connect with other traders.</p>
                                <a href="https://t.me/forexclass" class="btn btn-primary" target="_blank">
                                    <i class="fab fa-telegram"></i>
                                    Join Telegram Channel
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
