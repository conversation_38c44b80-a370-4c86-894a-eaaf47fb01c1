<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Function to verify admin session
function verifyAdminSession() {
    $headers = getallheaders();
    $token = null;

    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    }

    if (!$token) {
        sendJsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
    }

    try {
        $pdo = getDBConnection();

        // Verify admin token using user_sessions table (same as regular users but check role)
        $stmt = $pdo->prepare("
            SELECT u.*, us.expires_at
            FROM users u
            JOIN user_sessions us ON u.id = us.user_id
            WHERE us.token = ? AND us.expires_at > ? AND u.status = 'active' AND u.role = 'admin'
        ");
        $stmt->execute([$token, date('Y-m-d H:i:s')]);
        $admin = $stmt->fetch();

        if (!$admin) {
            sendJsonResponse(['success' => false, 'message' => 'Invalid or expired admin session'], 401);
        }

        return $admin;
    } catch (Exception $e) {
        logError("Admin session verification error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Authentication failed'], 500);
    }
}

// Get dashboard overview data
function getDashboardOverview() {
    $admin = verifyAdminSession();
    
    try {
        $pdo = getDBConnection();
        
        // Get total users
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM users WHERE status = 'active'");
        $stmt->execute();
        $totalUsers = $stmt->fetch()['total'];
        
        // Get new users this month
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total FROM users 
            WHERE status = 'active' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
        ");
        $stmt->execute();
        $newUsersThisMonth = $stmt->fetch()['total'];
        
        // Get active subscriptions
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total FROM user_subscriptions 
            WHERE status = 'active' AND end_date > NOW()
        ");
        $stmt->execute();
        $activeSubscriptions = $stmt->fetch()['total'];
        
        // Get total revenue this month
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(amount), 0) as total FROM transactions 
            WHERE status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
        ");
        $stmt->execute();
        $monthlyRevenue = $stmt->fetch()['total'];
        
        // Get total revenue
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(amount), 0) as total FROM transactions 
            WHERE status = 'completed'
        ");
        $stmt->execute();
        $totalRevenue = $stmt->fetch()['total'];
        
        // Get subscription breakdown
        $stmt = $pdo->prepare("
            SELECT 
                us.plan_type,
                COUNT(*) as count,
                sp.name as plan_name
            FROM user_subscriptions us
            JOIN subscription_plans sp ON us.plan_type = sp.plan_type
            WHERE us.status = 'active' AND us.end_date > NOW()
            GROUP BY us.plan_type, sp.name
        ");
        $stmt->execute();
        $subscriptionBreakdown = $stmt->fetchAll();
        
        // Get recent transactions
        $stmt = $pdo->prepare("
            SELECT 
                t.*,
                u.name as user_name,
                u.email as user_email,
                us.plan_type
            FROM transactions t
            JOIN users u ON t.user_id = u.id
            LEFT JOIN user_subscriptions us ON t.subscription_id = us.id
            WHERE t.status = 'completed'
            ORDER BY t.created_at DESC
            LIMIT 10
        ");
        $stmt->execute();
        $recentTransactions = $stmt->fetchAll();
        
        // Get monthly revenue chart data (last 6 months)
        $stmt = $pdo->prepare("
            SELECT 
                DATE_FORMAT(created_at, '%Y-%m') as month,
                SUM(amount) as revenue
            FROM transactions 
            WHERE status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month ASC
        ");
        $stmt->execute();
        $monthlyRevenueChart = $stmt->fetchAll();
        
        // Get user growth chart data (last 6 months)
        $stmt = $pdo->prepare("
            SELECT 
                DATE_FORMAT(created_at, '%Y-%m') as month,
                COUNT(*) as users
            FROM users 
            WHERE status = 'active' AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month ASC
        ");
        $stmt->execute();
        $userGrowthChart = $stmt->fetchAll();
        
        sendJsonResponse([
            'success' => true,
            'data' => [
                'overview' => [
                    'total_users' => $totalUsers,
                    'new_users_this_month' => $newUsersThisMonth,
                    'active_subscriptions' => $activeSubscriptions,
                    'monthly_revenue' => $monthlyRevenue,
                    'total_revenue' => $totalRevenue
                ],
                'subscription_breakdown' => $subscriptionBreakdown,
                'recent_transactions' => $recentTransactions,
                'charts' => [
                    'monthly_revenue' => $monthlyRevenueChart,
                    'user_growth' => $userGrowthChart
                ]
            ]
        ]);
        
    } catch (Exception $e) {
        logError("Dashboard overview error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to load dashboard data'], 500);
    }
}

// Get users data
function getUsersData() {
    $admin = verifyAdminSession();
    
    try {
        $pdo = getDBConnection();
        
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        $search = $_GET['search'] ?? '';
        $status = $_GET['status'] ?? '';
        
        $offset = ($page - 1) * $limit;
        
        // Build query
        $whereConditions = [];
        $params = [];
        
        if (!empty($search)) {
            $whereConditions[] = "(u.name LIKE ? OR u.email LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($status)) {
            $whereConditions[] = "u.status = ?";
            $params[] = $status;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM users u $whereClause";
        $stmt = $pdo->prepare($countSql);
        $stmt->execute($params);
        $totalUsers = $stmt->fetch()['total'];
        
        // Get users with subscription info
        $sql = "
            SELECT 
                u.*,
                us.plan_type,
                us.status as subscription_status,
                us.end_date as subscription_end_date,
                sp.name as plan_name
            FROM users u
            LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
            LEFT JOIN subscription_plans sp ON us.plan_type = sp.plan_type
            $whereClause
            ORDER BY u.created_at DESC
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $users = $stmt->fetchAll();
        
        sendJsonResponse([
            'success' => true,
            'data' => [
                'users' => $users,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => ceil($totalUsers / $limit),
                    'total_users' => $totalUsers,
                    'per_page' => $limit
                ]
            ]
        ]);
        
    } catch (Exception $e) {
        logError("Users data error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to load users data'], 500);
    }
}

// Handle different request methods and actions
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

switch ($method) {
    case 'GET':
        if ($action === 'overview') {
            getDashboardOverview();
        } elseif ($action === 'users') {
            getUsersData();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;
        
    default:
        sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
        break;
}
?>
