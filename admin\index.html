<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - ForexClass</title>
    <link rel="stylesheet" href="admin-login.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <div class="admin-login-container">
        <div class="login-background">
            <div class="background-pattern"></div>
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
            </div>
        </div>
        
        <div class="login-content">
            <div class="login-card">
                <div class="login-header">
                    <div class="admin-logo">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h1>Admin Portal</h1>
                    <p>ForexClass Administration</p>
                </div>
                
                <form id="adminLoginForm" class="login-form">
                    <div class="form-group">
                        <label for="adminEmail">
                            <i class="fas fa-envelope"></i>
                            Email Address
                        </label>
                        <input type="email" id="adminEmail" name="email" placeholder="Enter admin email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="adminPassword">
                            <i class="fas fa-lock"></i>
                            Password
                        </label>
                        <div class="password-input">
                            <input type="password" id="adminPassword" name="password" placeholder="Enter admin password" required>
                            <button type="button" class="password-toggle" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="rememberMe" name="remember">
                            <span class="checkmark"></span>
                            Remember me for 30 days
                        </label>
                    </div>
                    
                    <button type="submit" class="login-btn" id="loginBtn">
                        <span class="btn-text">Sign In to Admin Panel</span>
                        <div class="btn-loader" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </button>
                </form>
                
                <div class="alert" id="loginAlert" style="display: none;">
                    <div class="alert-content">
                        <i class="alert-icon"></i>
                        <span class="alert-message"></span>
                    </div>
                </div>
                
                <div class="login-footer">
                    <div class="security-info">
                        <i class="fas fa-shield-check"></i>
                        <span>Secure Admin Access</span>
                    </div>
                    <div class="back-link">
                        <a href="../index.html">
                            <i class="fas fa-arrow-left"></i>
                            Back to Website
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="admin-info">
                <h3>Admin Access Only</h3>
                <div class="info-list">
                    <div class="info-item">
                        <i class="fas fa-users"></i>
                        <span>User Management</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics Dashboard</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-cog"></i>
                        <span>System Settings</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-crown"></i>
                        <span>Subscription Management</span>
                    </div>
                </div>
                
                <div class="demo-credentials">
                    <h4>Admin Credentials:</h4>
                    <div class="credentials">
                        <div class="credential-item">
                            <strong>Email:</strong> <EMAIL>
                        </div>
                        <div class="credential-item">
                            <strong>Password:</strong> admin@123
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="admin-login.js"></script>
</body>
</html>
