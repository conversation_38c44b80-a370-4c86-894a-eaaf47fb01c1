🚀 FOREXCLASS UPLOAD INSTRUCTIONS
=====================================

📁 UPLOAD THESE FILES TO: forexhub.quatromgt.co.ke

STEP 1: <PERSON><PERSON><PERSON><PERSON> TO CPANEL
- Go to your hosting control panel
- Login with your credentials

STEP 2: OPEN FILE MANAGER
- Click "File Manager" in cPanel
- Navigate to "public_html" folder

STEP 3: UPLOAD FILES
Upload these files to public_html:

✅ index.html          (Main website)
✅ styles.min.css      (Website styles)  
✅ script.js           (Website JavaScript)
✅ api/mpesa.php       (M-Pesa payment handler)
✅ api/callback.php    (Payment callback handler)

STEP 4: CREATE API FOLDER
- In public_html, create folder named "api"
- Upload mpesa.php and callback.php to this api folder

STEP 5: TEST YOUR WEBSITE
- Visit: https://forexhub.quatromgt.co.ke
- Go to "Choose Your Plan" section
- Test with phone: 254708374149
- PIN: 1234 (sandbox)

🎯 EXPECTED RESULT:
✅ Beautiful ForexClass website loads
✅ M-Pesa payment works with sandbox
✅ No real money charged (safe testing)
✅ Responsive design on all devices

🔧 TROUBLESHOOTING:
- If M-Pesa doesn't work, check if PHP cURL is enabled
- Ensure file permissions are correct (644 for files)
- Check cPanel error logs if issues occur

📱 SANDBOX TESTING:
- Phone numbers: 254708374149, 254711111111
- PIN: 1234
- Safe for public testing - no real charges

🎉 Your ForexClass website with M-Pesa integration is ready!

Need help? Check the error logs in cPanel or contact your hosting support.
