<?php
/**
 * Test Subscription Plans Sync
 * 
 * Simple test page to verify subscription plans are working across admin, student, and website
 */

require_once 'api/config.php';

try {
    $pdo = getDBConnection();
    
    // Get all subscription plans
    $stmt = $pdo->query("SELECT * FROM subscription_plans ORDER BY price_monthly ASC");
    $plans = $stmt->fetchAll();
    
    echo "<h1>Subscription Plans Test</h1>";
    echo "<p>Testing if plans sync between Admin Dashboard, Student Portal, and Main Website</p>";
    
    if (empty($plans)) {
        echo "<h2>No Plans Found</h2>";
        echo "<p>Create some plans in the admin dashboard first.</p>";
        echo "<a href='admin/dashboard.php'>Go to Admin Dashboard</a>";
    } else {
        echo "<h2>Found " . count($plans) . " Plans:</h2>";
        
        foreach ($plans as $plan) {
            echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 15px; border-radius: 5px;'>";
            echo "<h3>" . htmlspecialchars($plan['name']) . " (" . htmlspecialchars($plan['plan_type']) . ")</h3>";
            echo "<p>" . htmlspecialchars($plan['description']) . "</p>";
            echo "<p><strong>Monthly:</strong> KSH " . number_format($plan['price_monthly']) . "</p>";
            echo "<p><strong>Yearly:</strong> KSH " . number_format($plan['price_yearly']) . "</p>";
            echo "<p><strong>Telegram Access:</strong> " . ($plan['telegram_access'] ? 'Yes' : 'No') . "</p>";
            echo "<p><strong>Signal Access:</strong> " . ($plan['signal_access'] ? 'Yes' : 'No') . "</p>";
            echo "<p><strong>Status:</strong> " . ($plan['is_active'] ? 'Active' : 'Inactive') . "</p>";
            
            $features = json_decode($plan['features'], true);
            if ($features) {
                echo "<p><strong>Features:</strong></p>";
                echo "<ul>";
                foreach ($features as $feature) {
                    echo "<li>" . htmlspecialchars($feature) . "</li>";
                }
                echo "</ul>";
            }
            echo "</div>";
        }
        
        echo "<h2>Test Links:</h2>";
        echo "<ul>";
        echo "<li><a href='admin/dashboard.php'>Admin Dashboard</a> - Manage plans here</li>";
        echo "<li><a href='student/subscription.php'>Student Subscription Page</a> - Plans should appear here</li>";
        echo "<li><a href='index.html'>Main Website</a> - Plans should appear in pricing section</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<h1>Database Error</h1>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2, h3 {
    color: #333;
}
a {
    color: #4f46e5;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
