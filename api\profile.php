<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Function to verify user session
function verifyUserSession() {
    $headers = getallheaders();
    $token = null;
    
    // Check for Authorization header
    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    }
    
    if (!$token) {
        sendJsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
    }
    
    try {
        $pdo = getDBConnection();
        
        // Verify token and get user
        $stmt = $pdo->prepare("
            SELECT u.*, us.expires_at 
            FROM users u 
            JOIN user_sessions us ON u.id = us.user_id 
            WHERE us.token = ? AND us.expires_at > ? AND u.status = 'active'
        ");
        $stmt->execute([$token, date('Y-m-d H:i:s')]);
        $user = $stmt->fetch();
        
        if (!$user) {
            sendJsonResponse(['success' => false, 'message' => 'Invalid or expired session'], 401);
        }
        
        return $user;
    } catch (Exception $e) {
        logError("Session verification error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Authentication failed'], 500);
    }
}

// Get user profile
function getUserProfile() {
    $user = verifyUserSession();
    
    try {
        $pdo = getDBConnection();
        
        // Get user profile data
        $stmt = $pdo->prepare("
            SELECT u.id, u.name, u.email, u.phone, u.role, u.email_verified, u.phone_verified, 
                   u.status, u.created_at, u.last_login,
                   up.avatar, up.bio, up.trading_experience, up.preferred_markets, up.timezone
            FROM users u
            LEFT JOIN user_profiles up ON u.id = up.user_id
            WHERE u.id = ?
        ");
        $stmt->execute([$user['id']]);
        $profile = $stmt->fetch();
        
        if ($profile) {
            // Parse JSON fields
            $profile['preferred_markets'] = $profile['preferred_markets'] ? 
                json_decode($profile['preferred_markets'], true) : [];
        }
        
        sendJsonResponse([
            'success' => true,
            'profile' => $profile
        ]);
        
    } catch (Exception $e) {
        logError("Get profile error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to load profile'], 500);
    }
}

// Update personal information
function updatePersonalInfo() {
    $user = verifyUserSession();
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid input data'], 400);
    }
    
    $name = trim($input['name'] ?? '');
    $email = trim($input['email'] ?? '');
    $phone = trim($input['phone'] ?? '');
    $timezone = trim($input['timezone'] ?? 'UTC');
    
    // Validation
    if (empty($name) || strlen($name) < 2) {
        sendJsonResponse(['success' => false, 'message' => 'Name must be at least 2 characters'], 400);
    }
    
    if (!validateEmail($email)) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid email format'], 400);
    }
    
    if (!empty($phone) && !validatePhone($phone)) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid phone number format'], 400);
    }
    
    try {
        $pdo = getDBConnection();
        
        // Check if email is already taken by another user
        if ($email !== $user['email']) {
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $user['id']]);
            if ($stmt->fetch()) {
                sendJsonResponse(['success' => false, 'message' => 'Email already in use'], 409);
            }
        }
        
        // Check if phone is already taken by another user
        if (!empty($phone) && $phone !== $user['phone']) {
            $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = ? AND id != ?");
            $stmt->execute([$phone, $user['id']]);
            if ($stmt->fetch()) {
                sendJsonResponse(['success' => false, 'message' => 'Phone number already in use'], 409);
            }
        }
        
        // Update user information
        $stmt = $pdo->prepare("
            UPDATE users 
            SET name = ?, email = ?, phone = ?, updated_at = ?
            WHERE id = ?
        ");
        $stmt->execute([$name, $email, $phone, date('Y-m-d H:i:s'), $user['id']]);
        
        // Update or create user profile
        $stmt = $pdo->prepare("
            INSERT INTO user_profiles (user_id, timezone, updated_at) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            timezone = VALUES(timezone), 
            updated_at = VALUES(updated_at)
        ");
        $stmt->execute([$user['id'], $timezone, date('Y-m-d H:i:s')]);
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Personal information updated successfully'
        ]);
        
    } catch (Exception $e) {
        logError("Update personal info error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to update information'], 500);
    }
}

// Update trading preferences
function updateTradingPreferences() {
    $user = verifyUserSession();
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid input data'], 400);
    }
    
    $tradingExperience = $input['trading_experience'] ?? 'beginner';
    $preferredMarkets = $input['preferred_markets'] ?? [];
    $bio = trim($input['bio'] ?? '');
    
    // Validate trading experience
    $validExperience = ['beginner', 'intermediate', 'advanced'];
    if (!in_array($tradingExperience, $validExperience)) {
        $tradingExperience = 'beginner';
    }
    
    // Validate preferred markets
    $validMarkets = ['forex', 'crypto', 'stocks', 'commodities'];
    $preferredMarkets = array_intersect($preferredMarkets, $validMarkets);
    
    try {
        $pdo = getDBConnection();
        
        // Update or create user profile
        $stmt = $pdo->prepare("
            INSERT INTO user_profiles (user_id, bio, trading_experience, preferred_markets, updated_at) 
            VALUES (?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            bio = VALUES(bio),
            trading_experience = VALUES(trading_experience),
            preferred_markets = VALUES(preferred_markets),
            updated_at = VALUES(updated_at)
        ");
        $stmt->execute([
            $user['id'], 
            $bio, 
            $tradingExperience, 
            json_encode($preferredMarkets), 
            date('Y-m-d H:i:s')
        ]);
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Trading preferences updated successfully'
        ]);
        
    } catch (Exception $e) {
        logError("Update trading preferences error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to update preferences'], 500);
    }
}

// Update password
function updatePassword() {
    $user = verifyUserSession();
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid input data'], 400);
    }
    
    $currentPassword = $input['current_password'] ?? '';
    $newPassword = $input['new_password'] ?? '';
    $confirmPassword = $input['confirm_password'] ?? '';
    
    // Validation
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        sendJsonResponse(['success' => false, 'message' => 'All password fields are required'], 400);
    }
    
    if ($newPassword !== $confirmPassword) {
        sendJsonResponse(['success' => false, 'message' => 'New passwords do not match'], 400);
    }
    
    if (strlen($newPassword) < 6) {
        sendJsonResponse(['success' => false, 'message' => 'Password must be at least 6 characters'], 400);
    }
    
    try {
        $pdo = getDBConnection();
        
        // Verify current password
        if (!password_verify($currentPassword, $user['password'])) {
            sendJsonResponse(['success' => false, 'message' => 'Current password is incorrect'], 400);
        }
        
        // Update password
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = ? WHERE id = ?");
        $stmt->execute([$hashedPassword, date('Y-m-d H:i:s'), $user['id']]);
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Password updated successfully'
        ]);
        
    } catch (Exception $e) {
        logError("Update password error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to update password'], 500);
    }
}

// Handle different request methods and actions
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

switch ($method) {
    case 'GET':
        if ($action === 'profile') {
            getUserProfile();
        } elseif ($action === 'download_payment_history') {
            downloadPaymentHistory();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;

    case 'POST':
    case 'PUT':
        $input = json_decode(file_get_contents('php://input'), true);
        $postAction = $input['action'] ?? $action;

        if ($postAction === 'personal-info') {
            updatePersonalInfo();
        } elseif ($postAction === 'trading-preferences') {
            updateTradingPreferences();
        } elseif ($postAction === 'password') {
            updatePassword();
        } elseif ($postAction === 'get_payment_history') {
            getPaymentHistory($input);
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;

    default:
        sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
        break;
}

// Get payment history for user
function getPaymentHistory($input) {
    $user_id = $input['user_id'] ?? null;

    if (!$user_id) {
        sendJsonResponse(['success' => false, 'message' => 'User ID required'], 400);
    }

    try {
        $pdo = getDBConnection();

        // Get payment summary
        $stmt = $pdo->prepare("
            SELECT
                COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_spent,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as total_payments
            FROM transactions
            WHERE user_id = ?
        ");
        $stmt->execute([$user_id]);
        $summary = $stmt->fetch();

        // Get active subscription
        $stmt = $pdo->prepare("
            SELECT us.*, sp.name as plan_name
            FROM user_subscriptions us
            JOIN subscription_plans sp ON us.plan_type = sp.plan_type
            WHERE us.user_id = ? AND us.status = 'active' AND us.end_date > NOW()
            ORDER BY us.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$user_id]);
        $activeSubscription = $stmt->fetch();

        // Get recent transactions
        $stmt = $pdo->prepare("
            SELECT
                t.*,
                sp.name as plan_name,
                us.billing_cycle
            FROM transactions t
            LEFT JOIN user_subscriptions us ON t.subscription_id = us.id
            LEFT JOIN subscription_plans sp ON us.plan_type = sp.plan_type
            WHERE t.user_id = ?
            ORDER BY t.created_at DESC
            LIMIT 20
        ");
        $stmt->execute([$user_id]);
        $transactions = $stmt->fetchAll();

        // Prepare summary data
        $summaryData = [
            'total_spent' => $summary['total_spent'],
            'active_subscription' => $activeSubscription ? $activeSubscription['plan_name'] : null,
            'next_payment' => $activeSubscription ? date('M j, Y', strtotime($activeSubscription['end_date'])) : null
        ];

        sendJsonResponse([
            'success' => true,
            'summary' => $summaryData,
            'transactions' => $transactions
        ]);

    } catch (Exception $e) {
        logError("Payment history error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to load payment history'], 500);
    }
}

// Download payment history as CSV
function downloadPaymentHistory() {
    $user_id = $_GET['user_id'] ?? null;

    if (!$user_id) {
        http_response_code(400);
        echo 'User ID required';
        exit();
    }

    try {
        $pdo = getDBConnection();

        $stmt = $pdo->prepare("
            SELECT
                t.created_at,
                t.transaction_id,
                t.amount,
                t.status,
                t.description,
                sp.name as plan_name,
                us.billing_cycle
            FROM transactions t
            LEFT JOIN user_subscriptions us ON t.subscription_id = us.id
            LEFT JOIN subscription_plans sp ON us.plan_type = sp.plan_type
            WHERE t.user_id = ?
            ORDER BY t.created_at DESC
        ");
        $stmt->execute([$user_id]);
        $transactions = $stmt->fetchAll();

        // Set CSV headers
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="payment_history_' . date('Y-m-d') . '.csv"');

        // Output CSV
        $output = fopen('php://output', 'w');

        // CSV headers
        fputcsv($output, ['Date', 'Transaction ID', 'Plan', 'Amount', 'Status', 'Description']);

        // CSV data
        foreach ($transactions as $transaction) {
            fputcsv($output, [
                date('Y-m-d H:i:s', strtotime($transaction['created_at'])),
                $transaction['transaction_id'],
                $transaction['plan_name'] ?: 'N/A',
                'KSH ' . number_format($transaction['amount'], 2),
                ucfirst($transaction['status']),
                $transaction['description'] ?: 'Subscription payment'
            ]);
        }

        fclose($output);
        exit();

    } catch (Exception $e) {
        http_response_code(500);
        echo 'Failed to generate payment history';
        exit();
    }
}
?>
