// Student Login JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeStudentLogin();
});

function initializeStudentLogin() {
    const loginForm = document.getElementById('studentLoginForm');
    
    if (loginForm) {
        loginForm.addEventListener('submit', handleStudentLogin);
    }
    
    // Check if already logged in
    checkExistingSession();
}

function checkExistingSession() {
    const userInfo = localStorage.getItem('userInfo');
    const userToken = localStorage.getItem('userToken');
    
    if (userInfo && userToken) {
        try {
            const user = JSON.parse(userInfo);
            if (user.role === 'user') {
                // User is already logged in, redirect to dashboard
                window.location.href = '/Forex/student/dashboard';
            }
        } catch (error) {
            // Invalid user info, clear storage
            localStorage.removeItem('userInfo');
            localStorage.removeItem('userToken');
        }
    }
}

async function handleStudentLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    const remember = document.getElementById('remember').checked;
    
    // Validation
    if (!email || !password) {
        showAlert('Please fill in all fields', 'error');
        return;
    }
    
    if (!isValidEmail(email)) {
        showAlert('Please enter a valid email address', 'error');
        return;
    }
    
    // Show loading state
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
    submitBtn.disabled = true;
    
    try {
        const response = await fetch('../api/login.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: email,
                password: password,
                remember: remember
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Store user information
            localStorage.setItem('userInfo', JSON.stringify(data.user));
            localStorage.setItem('userToken', data.user.token);
            
            // Check user role
            if (data.user.role !== 'user') {
                showAlert('Access denied. This portal is for students only.', 'error');
                return;
            }
            
            // Show success message
            showAlert('Login successful! Redirecting to dashboard...', 'success');
            
            // Redirect to dashboard
            setTimeout(() => {
                window.location.href = '/Forex/student/dashboard';
            }, 1500);
            
        } else {
            showAlert(data.message || 'Login failed. Please try again.', 'error');
        }
        
    } catch (error) {
        console.error('Login error:', error);
        showAlert('Network error. Please check your connection and try again.', 'error');
    } finally {
        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showAlert(message, type = 'info') {
    const modal = document.getElementById('alertModal');
    const icon = document.getElementById('alertIcon');
    const title = document.getElementById('alertTitle');
    const messageEl = document.getElementById('alertMessage');
    const content = modal.querySelector('.alert-content');
    
    // Set content
    messageEl.textContent = message;
    
    // Set type-specific content
    if (type === 'success') {
        icon.innerHTML = '<i class="fas fa-check-circle"></i>';
        title.textContent = 'Success';
        content.className = 'alert-content success';
    } else if (type === 'error') {
        icon.innerHTML = '<i class="fas fa-exclamation-circle"></i>';
        title.textContent = 'Error';
        content.className = 'alert-content error';
    } else {
        icon.innerHTML = '<i class="fas fa-info-circle"></i>';
        title.textContent = 'Information';
        content.className = 'alert-content';
    }
    
    // Show modal
    modal.style.display = 'flex';
    
    // Auto-close success messages
    if (type === 'success') {
        setTimeout(() => {
            closeAlert();
        }, 2000);
    }
}

function closeAlert() {
    const modal = document.getElementById('alertModal');
    modal.style.display = 'none';
}

// Handle Enter key in form fields
document.addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
        const form = event.target.closest('form');
        if (form && form.id === 'studentLoginForm') {
            event.preventDefault();
            form.dispatchEvent(new Event('submit'));
        }
    }
});

// Handle forgot password link
document.addEventListener('click', function(event) {
    if (event.target.matches('a[href="#forgot-password"]')) {
        event.preventDefault();
        showAlert('Password reset functionality will be available soon. Please contact support for assistance.', 'info');
    }
});
