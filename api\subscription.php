<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Function to verify user session
function verifyUserSession() {
    $headers = getallheaders();
    $token = null;
    
    // Check for Authorization header
    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    }
    
    if (!$token) {
        sendJsonResponse(['success' => false, 'message' => 'Authentication required'], 401);
    }
    
    try {
        $pdo = getDBConnection();
        
        // Verify token and get user
        $stmt = $pdo->prepare("
            SELECT u.*, us.expires_at 
            FROM users u 
            JOIN user_sessions us ON u.id = us.user_id 
            WHERE us.token = ? AND us.expires_at > ? AND u.status = 'active'
        ");
        $stmt->execute([$token, date('Y-m-d H:i:s')]);
        $user = $stmt->fetch();
        
        if (!$user) {
            sendJsonResponse(['success' => false, 'message' => 'Invalid or expired session'], 401);
        }
        
        return $user;
    } catch (Exception $e) {
        logError("Session verification error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Authentication failed'], 500);
    }
}

// Get user subscription details
function getUserSubscription() {
    $user = verifyUserSession();
    
    try {
        $pdo = getDBConnection();
        
        // Get current active subscription
        $stmt = $pdo->prepare("
            SELECT us.*, sp.name as plan_name, sp.plan_type, sp.price_monthly, sp.price_yearly, sp.features
            FROM user_subscriptions us
            JOIN subscription_plans sp ON us.plan_type = sp.plan_type
            WHERE us.user_id = ? AND us.status = 'active'
            ORDER BY us.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$user['id']]);
        $subscription = $stmt->fetch();
        
        // Get billing history
        $stmt = $pdo->prepare("
            SELECT t.*, us.plan_type
            FROM transactions t
            LEFT JOIN user_subscriptions us ON t.subscription_id = us.id
            WHERE t.user_id = ?
            ORDER BY t.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$user['id']]);
        $billingHistory = $stmt->fetchAll();
        
        // Get available plans
        $stmt = $pdo->prepare("SELECT * FROM subscription_plans WHERE is_active = TRUE ORDER BY price_monthly ASC");
        $stmt->execute();
        $availablePlans = $stmt->fetchAll();
        
        // Parse features JSON
        foreach ($availablePlans as &$plan) {
            $plan['features'] = $plan['features'] ? json_decode($plan['features'], true) : [];
        }
        
        if ($subscription) {
            $subscription['features'] = $subscription['features'] ? json_decode($subscription['features'], true) : [];
        }
        
        sendJsonResponse([
            'success' => true,
            'subscription' => $subscription,
            'billing_history' => $billingHistory,
            'available_plans' => $availablePlans
        ]);
        
    } catch (Exception $e) {
        logError("Get subscription error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to load subscription details'], 500);
    }
}

// Create new subscription
function createSubscription() {
    $user = verifyUserSession();
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid input data'], 400);
    }
    
    $planType = $input['plan_type'] ?? '';
    $billingCycle = $input['billing_cycle'] ?? 'monthly'; // monthly or yearly
    $paymentMethod = $input['payment_method'] ?? 'mpesa';
    
    // Validate plan type
    $validPlans = ['basic', 'premium', 'vip'];
    if (!in_array($planType, $validPlans)) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid plan type'], 400);
    }
    
    try {
        $pdo = getDBConnection();
        
        // Get plan details
        $stmt = $pdo->prepare("SELECT * FROM subscription_plans WHERE plan_type = ? AND is_active = TRUE");
        $stmt->execute([$planType]);
        $plan = $stmt->fetch();
        
        if (!$plan) {
            sendJsonResponse(['success' => false, 'message' => 'Plan not found'], 404);
        }
        
        // Calculate amount and end date
        $amount = $billingCycle === 'yearly' ? $plan['price_yearly'] : $plan['price_monthly'];
        $duration = $billingCycle === 'yearly' ? '+1 year' : '+1 month';
        $endDate = date('Y-m-d H:i:s', strtotime($duration));
        
        // Generate transaction ID
        $transactionId = 'TXN_' . time() . '_' . $user['id'];
        
        // Start transaction
        $pdo->beginTransaction();
        
        // Cancel any existing active subscriptions
        $stmt = $pdo->prepare("UPDATE user_subscriptions SET status = 'cancelled', cancelled_at = ? WHERE user_id = ? AND status = 'active'");
        $stmt->execute([date('Y-m-d H:i:s'), $user['id']]);
        
        // Create new subscription
        $stmt = $pdo->prepare("
            INSERT INTO user_subscriptions (user_id, plan_type, status, amount, currency, start_date, end_date, payment_method, transaction_id, created_at)
            VALUES (?, ?, 'pending', ?, 'KES', ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $user['id'], 
            $planType, 
            $amount, 
            date('Y-m-d H:i:s'), 
            $endDate, 
            $paymentMethod, 
            $transactionId,
            date('Y-m-d H:i:s')
        ]);
        
        $subscriptionId = $pdo->lastInsertId();
        
        // Create transaction record
        $stmt = $pdo->prepare("
            INSERT INTO transactions (user_id, subscription_id, transaction_id, amount, currency, payment_method, status, description, created_at)
            VALUES (?, ?, ?, ?, 'KES', ?, 'pending', ?, ?)
        ");
        $stmt->execute([
            $user['id'],
            $subscriptionId,
            $transactionId,
            $amount,
            $paymentMethod,
            "Subscription to {$plan['name']} - {$billingCycle}",
            date('Y-m-d H:i:s')
        ]);
        
        $pdo->commit();
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Subscription created successfully',
            'subscription_id' => $subscriptionId,
            'transaction_id' => $transactionId,
            'amount' => $amount,
            'payment_url' => "payment.php?transaction_id={$transactionId}" // You can implement payment gateway here
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        logError("Create subscription error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to create subscription'], 500);
    }
}

// Update subscription settings
function updateSubscriptionSettings() {
    $user = verifyUserSession();
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid input data'], 400);
    }
    
    $autoRenew = isset($input['auto_renew']) ? (bool)$input['auto_renew'] : null;
    
    try {
        $pdo = getDBConnection();
        
        // Update current active subscription
        if ($autoRenew !== null) {
            $stmt = $pdo->prepare("
                UPDATE user_subscriptions 
                SET auto_renew = ?, updated_at = ?
                WHERE user_id = ? AND status = 'active'
            ");
            $stmt->execute([$autoRenew, date('Y-m-d H:i:s'), $user['id']]);
        }
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Subscription settings updated successfully'
        ]);
        
    } catch (Exception $e) {
        logError("Update subscription settings error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to update settings'], 500);
    }
}

// Cancel subscription
function cancelSubscription() {
    $user = verifyUserSession();
    $input = json_decode(file_get_contents('php://input'), true);
    
    $reason = $input['reason'] ?? 'User requested cancellation';
    
    try {
        $pdo = getDBConnection();
        
        // Cancel current active subscription
        $stmt = $pdo->prepare("
            UPDATE user_subscriptions 
            SET status = 'cancelled', cancellation_reason = ?, cancelled_at = ?, updated_at = ?
            WHERE user_id = ? AND status = 'active'
        ");
        $stmt->execute([$reason, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), $user['id']]);
        
        if ($stmt->rowCount() > 0) {
            sendJsonResponse([
                'success' => true,
                'message' => 'Subscription cancelled successfully. You will continue to have access until the end of your billing period.'
            ]);
        } else {
            sendJsonResponse(['success' => false, 'message' => 'No active subscription found'], 404);
        }
        
    } catch (Exception $e) {
        logError("Cancel subscription error: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Failed to cancel subscription'], 500);
    }
}

// Handle different request methods and actions
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

switch ($method) {
    case 'GET':
        if ($action === 'details') {
            getUserSubscription();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;
        
    case 'POST':
        if ($action === 'create') {
            createSubscription();
        } elseif ($action === 'cancel') {
            cancelSubscription();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;
        
    case 'PUT':
        if ($action === 'settings') {
            updateSubscriptionSettings();
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
        break;
        
    default:
        sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
        break;
}
?>
